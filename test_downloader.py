#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MD下载器的基本功能
"""

import sys
from pathlib import Path
from md_downloader import DownloadManager, Logger

def test_single_product():
    """测试下载单个产品"""
    print("测试下载单个产品: tm")
    
    # 设置日志
    logger = Logger.setup_logger()
    
    # 创建下载目录
    Path("downloads").mkdir(exist_ok=True)
    
    # 创建下载管理器
    manager = DownloadManager()
    
    try:
        # 只下载tm产品
        manager.run(products=['tm'], force_download=False)
        print("测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    
    return True

def test_config_fetch():
    """测试配置获取"""
    print("测试配置获取...")
    
    from md_downloader import HTTPClient, ConfigFetcher
    
    http_client = HTTPClient()
    config_fetcher = ConfigFetcher(http_client)
    
    try:
        config = config_fetcher.fetch_config()
        print(f"获取到 {len(config)} 个产品配置:")
        for key in list(config.keys())[:5]:  # 只显示前5个
            print(f"  - {key}: {config[key]}")
        if len(config) > 5:
            print(f"  ... 还有 {len(config) - 5} 个")
        
        http_client.close()
        return True
        
    except Exception as e:
        print(f"配置获取失败: {e}")
        http_client.close()
        return False

def test_catalog_parse():
    """测试目录解析"""
    print("测试目录解析...")
    
    from md_downloader import HTTPClient, CatalogParser
    
    http_client = HTTPClient()
    catalog_parser = CatalogParser(http_client)
    
    try:
        # 测试解析tm产品的路径
        router_path = "/doc/#/doc/md/tm%2Ftm%2Fzh-Hans%2Foverview%2FProduct-Introduction.md"
        product, dir_path = catalog_parser.parse_router_path(router_path)
        print(f"解析路径: {router_path}")
        print(f"产品: {product}, 目录: {dir_path}")
        
        # 获取目录结构
        catalog_data = catalog_parser.get_catalog(dir_path)
        print(f"获取到 {len(catalog_data)} 个目录项")
        
        # 提取markdown文件
        markdown_files = catalog_parser.extract_markdown_files(catalog_data)
        print(f"提取到 {len(markdown_files)} 个markdown文件:")
        for md_file in markdown_files[:3]:  # 只显示前3个
            print(f"  - {md_file['name']}: {md_file['url']}")
        
        http_client.close()
        return True
        
    except Exception as e:
        print(f"目录解析失败: {e}")
        http_client.close()
        return False

if __name__ == "__main__":
    print("MD下载器功能测试")
    print("=" * 50)
    
    # 测试配置获取
    if not test_config_fetch():
        sys.exit(1)
    
    print()
    
    # 测试目录解析
    if not test_catalog_parse():
        sys.exit(1)
    
    print()
    
    # 询问是否进行完整测试
    response = input("是否进行完整下载测试？(y/N): ").strip().lower()
    if response == 'y':
        test_single_product()
    else:
        print("跳过完整下载测试")
    
    print("测试完成！")
