#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MD文档批量下载器
支持增量下载，自动提取和下载资源文件
"""

import os
import json
import re
import time
import logging
import urllib.parse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class Config:
    """配置类"""
    BASE_URL = "https://open.inspures.com"
    CONFIG_URL = f"{BASE_URL}/doc/assets/config/config.prod.json"
    CATALOG_URL = f"{BASE_URL}/api/open/opendoc/v1.0/catalog"
    DOC_BASE_URL = f"{BASE_URL}/doc/assets/docs"
    
    HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36'
    }
    
    # 下载配置
    DOWNLOAD_DIR = "downloads"
    METADATA_FILE = "metadata.json"
    LOG_DIR = "logs"
    LOG_FILE = "download.log"
    
    # 请求配置
    REQUEST_TIMEOUT = 30
    MAX_RETRIES = 3
    RETRY_BACKOFF_FACTOR = 1
    
    # 并发配置
    MAX_WORKERS = 5


class Logger:
    """日志管理器"""
    
    @staticmethod
    def setup_logger(log_dir: str = Config.LOG_DIR, log_file: str = Config.LOG_FILE) -> logging.Logger:
        """设置日志记录器"""
        # 创建日志目录
        Path(log_dir).mkdir(exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(
            Path(log_dir) / log_file, 
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 创建日志记录器
        logger = logging.getLogger('MDDownloader')
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger


class HTTPClient:
    """HTTP客户端，支持重试和SSL忽略"""
    
    def __init__(self, timeout: int = Config.REQUEST_TIMEOUT):
        self.session = requests.Session()
        self.session.verify = False  # 忽略SSL证书验证
        self.session.headers.update(Config.HEADERS)
        
        # 配置重试策略
        retry_strategy = Retry(
            total=Config.MAX_RETRIES,
            backoff_factor=Config.RETRY_BACKOFF_FACTOR,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        self.timeout = timeout
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求"""
        kwargs.setdefault('timeout', self.timeout)
        return self.session.get(url, **kwargs)
    
    def head(self, url: str, **kwargs) -> requests.Response:
        """HEAD请求"""
        kwargs.setdefault('timeout', self.timeout)
        return self.session.head(url, **kwargs)
    
    def close(self):
        """关闭会话"""
        self.session.close()


class MetadataManager:
    """元数据管理器，用于增量下载"""
    
    def __init__(self, metadata_file: str = Config.METADATA_FILE):
        self.metadata_file = Path(metadata_file)
        self.metadata = self._load_metadata()
        self.logger = logging.getLogger('MDDownloader.MetadataManager')
    
    def _load_metadata(self) -> Dict:
        """加载元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载元数据失败: {e}")
                return {}
        return {}
    
    def save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except IOError as e:
            self.logger.error(f"保存元数据失败: {e}")
    
    def get_file_info(self, url: str) -> Optional[Dict]:
        """获取文件信息"""
        return self.metadata.get(url)
    
    def update_file_info(self, url: str, last_modified: str, local_path: str, size: int = 0):
        """更新文件信息"""
        self.metadata[url] = {
            'last_modified': last_modified,
            'local_path': local_path,
            'size': size,
            'updated_at': datetime.now().isoformat()
        }
    
    def should_download(self, url: str, remote_last_modified: str) -> bool:
        """判断是否需要下载文件"""
        local_info = self.get_file_info(url)
        if not local_info:
            return True
        
        # 检查本地文件是否存在
        local_path = Path(local_info['local_path'])
        if not local_path.exists():
            return True
        
        # 比较修改时间
        return local_info.get('last_modified') != remote_last_modified


class ConfigFetcher:
    """配置获取器"""
    
    def __init__(self, http_client: HTTPClient):
        self.http_client = http_client
        self.logger = logging.getLogger('MDDownloader.ConfigFetcher')
    
    def fetch_config(self) -> Dict:
        """获取配置信息"""
        try:
            self.logger.info(f"获取配置: {Config.CONFIG_URL}")
            response = self.http_client.get(Config.CONFIG_URL)
            response.raise_for_status()
            
            config_data = response.json()
            build_router_prefix = config_data.get('buildRouterPrefix', {})
            
            self.logger.info(f"获取到 {len(build_router_prefix)} 个产品配置")
            return build_router_prefix
            
        except Exception as e:
            self.logger.error(f"获取配置失败: {e}")
            raise


class CatalogParser:
    """目录解析器"""

    def __init__(self, http_client: HTTPClient):
        self.http_client = http_client
        self.logger = logging.getLogger('MDDownloader.CatalogParser')

    def parse_router_path(self, router_path: str) -> Tuple[str, str]:
        """解析路由路径，提取产品和基础路径信息"""
        # 例如: "/doc/#/doc/md/tm%2Ftm%2Fzh-Hans%2Foverview%2FProduct-Introduction.md"
        if '/doc/md/' in router_path:
            path_part = router_path.split('/doc/md/')[-1]
            # URL解码
            decoded_path = urllib.parse.unquote(path_part)
            # 移除.md后缀
            if decoded_path.endswith('.md'):
                decoded_path = decoded_path[:-3]

            # 提取产品名称和基础路径
            path_segments = decoded_path.split('/')
            if len(path_segments) >= 3:
                product = path_segments[0]
                # 构建基础目录路径（只取前3段：product/product/language）
                # 例如：tm/tm/zh-Hans，不包含具体的功能目录
                base_path = '/'.join(path_segments[:3])
                return product, base_path

        return "", ""

    def get_catalog(self, path: str) -> List[Dict]:
        """获取指定路径的目录结构"""
        catalog_url = f"{Config.CATALOG_URL}?currentMenuPah=web/doc/assets/docs/{path}"

        try:
            self.logger.info(f"获取目录: {catalog_url}")
            response = self.http_client.get(catalog_url)
            response.raise_for_status()

            catalog_data = response.json()
            self.logger.info(f"获取到 {len(catalog_data)} 个目录项")
            return catalog_data

        except Exception as e:
            self.logger.error(f"获取目录失败 {catalog_url}: {e}")
            return []

    def extract_markdown_files(self, catalog_data: List[Dict]) -> List[Dict]:
        """从目录数据中提取所有markdown文件"""
        markdown_files = []

        def extract_recursive(items: List[Dict]):
            for item in items:
                if item.get('type') == 'markdown':
                    markdown_files.append({
                        'name': item.get('name', ''),
                        'originName': item.get('originName', ''),
                        'url': item.get('url', ''),
                        'layer': item.get('layer', 0)
                    })

                # 递归处理子菜单
                submenu = item.get('submenu')
                if submenu:
                    extract_recursive(submenu)

        extract_recursive(catalog_data)
        return markdown_files


class FileDownloader:
    """文件下载器，支持增量下载"""

    def __init__(self, http_client: HTTPClient, metadata_manager: MetadataManager):
        self.http_client = http_client
        self.metadata_manager = metadata_manager
        self.logger = logging.getLogger('MDDownloader.FileDownloader')

    def get_remote_file_info(self, url: str) -> Optional[Tuple[str, int]]:
        """获取远程文件信息（修改时间和大小）"""
        try:
            response = self.http_client.head(url)
            response.raise_for_status()

            last_modified = response.headers.get('Last-Modified', '')
            content_length = int(response.headers.get('Content-Length', 0))

            return last_modified, content_length

        except Exception as e:
            self.logger.warning(f"获取文件信息失败 {url}: {e}")
            return None

    def download_file(self, url: str, local_path: Path, force: bool = False) -> bool:
        """下载文件，支持增量下载"""
        try:
            # 获取远程文件信息
            remote_info = self.get_remote_file_info(url)
            if not remote_info:
                self.logger.warning(f"无法获取远程文件信息: {url}")
                return False

            remote_last_modified, remote_size = remote_info

            # 检查是否需要下载
            if not force and not self.metadata_manager.should_download(url, remote_last_modified):
                self.logger.info(f"文件无需更新: {local_path}")
                return True

            # 创建目录
            local_path.parent.mkdir(parents=True, exist_ok=True)

            # 下载文件
            self.logger.info(f"下载文件: {url} -> {local_path}")
            response = self.http_client.get(url)
            response.raise_for_status()

            # 保存文件
            with open(local_path, 'wb') as f:
                f.write(response.content)

            # 更新元数据
            self.metadata_manager.update_file_info(
                url, remote_last_modified, str(local_path), len(response.content)
            )

            self.logger.info(f"下载完成: {local_path}")
            return True

        except Exception as e:
            self.logger.error(f"下载失败 {url}: {e}")
            return False


class ResourceExtractor:
    """资源提取器，从MD文件中提取图片等资源"""

    def __init__(self, file_downloader: FileDownloader):
        self.file_downloader = file_downloader
        self.logger = logging.getLogger('MDDownloader.ResourceExtractor')

    def extract_resources_from_md(self, md_content: str, md_url: str) -> List[str]:
        """从MD内容中提取资源链接"""
        resources = []

        # 提取图片链接 ![alt](path) 和 ![](path)
        img_pattern = r'!\[.*?\]\(([^)]+)\)'
        img_matches = re.findall(img_pattern, md_content)

        # 提取链接中的资源 [text](path)，过滤掉http链接
        link_pattern = r'\[.*?\]\(([^)]+)\)'
        link_matches = re.findall(link_pattern, md_content)

        # 合并所有匹配
        all_matches = img_matches + link_matches

        for match in all_matches:
            # 跳过外部链接
            if match.startswith(('http://', 'https://', 'mailto:', '#')):
                continue

            # 构建完整的资源URL
            resource_url = self._build_resource_url(match, md_url)
            if resource_url:
                resources.append(resource_url)

        return list(set(resources))  # 去重

    def _build_resource_url(self, resource_path: str, md_url: str) -> Optional[str]:
        """构建资源的完整URL"""
        try:
            # 获取MD文件的基础路径
            base_url = '/'.join(md_url.split('/')[:-1])

            # 处理相对路径
            if resource_path.startswith('./'):
                resource_path = resource_path[2:]
            elif resource_path.startswith('../'):
                # 处理上级目录
                parts = base_url.split('/')
                up_levels = resource_path.count('../')
                resource_path = resource_path.replace('../', '', up_levels)
                if len(parts) >= up_levels:
                    base_url = '/'.join(parts[:-up_levels])

            # 构建完整URL
            if resource_path.startswith('/'):
                return f"{Config.BASE_URL}{resource_path}"
            else:
                return f"{base_url}/{resource_path}"

        except Exception as e:
            self.logger.warning(f"构建资源URL失败 {resource_path}: {e}")
            return None

    def download_resources(self, md_file_path: Path, md_url: str) -> int:
        """下载MD文件中的所有资源"""
        try:
            # 读取MD文件内容
            with open(md_file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()

            # 提取资源链接
            resources = self.extract_resources_from_md(md_content, md_url)

            if not resources:
                return 0

            self.logger.info(f"发现 {len(resources)} 个资源文件")

            downloaded_count = 0
            for resource_url in resources:
                # 构建本地路径
                local_resource_path = self._get_local_resource_path(resource_url, md_file_path)

                if self.file_downloader.download_file(resource_url, local_resource_path):
                    downloaded_count += 1

            return downloaded_count

        except Exception as e:
            self.logger.error(f"下载资源失败 {md_file_path}: {e}")
            return 0

    def _get_local_resource_path(self, resource_url: str, md_file_path: Path) -> Path:
        """获取资源的本地存储路径"""
        # 从URL中提取文件名
        resource_name = resource_url.split('/')[-1]

        # 构建相对于MD文件的路径
        md_dir = md_file_path.parent

        # 如果资源URL包含子目录（如images/），保持目录结构
        url_parts = resource_url.replace(Config.BASE_URL, '').split('/')
        if len(url_parts) > 1:
            # 找到相对于文档根目录的路径
            doc_base_index = -1
            for i, part in enumerate(url_parts):
                if part == 'docs':
                    doc_base_index = i
                    break

            if doc_base_index >= 0:
                # 重建相对路径
                relative_parts = url_parts[doc_base_index + 1:]
                # 移除产品重复路径部分，保留资源路径
                if len(relative_parts) > 3:  # 至少包含 product/product/lang/...
                    resource_relative_path = '/'.join(relative_parts[3:])
                    return md_dir.parent / resource_relative_path

        # 默认情况下，放在MD文件同目录下
        return md_dir / resource_name


class DownloadManager:
    """下载管理器，统一管理整个下载流程"""

    def __init__(self):
        self.logger = logging.getLogger('MDDownloader.DownloadManager')
        self.http_client = HTTPClient()
        self.metadata_manager = MetadataManager()
        self.config_fetcher = ConfigFetcher(self.http_client)
        self.catalog_parser = CatalogParser(self.http_client)
        self.file_downloader = FileDownloader(self.http_client, self.metadata_manager)
        self.resource_extractor = ResourceExtractor(self.file_downloader)

    def run(self, products: Optional[List[str]] = None, force_download: bool = False):
        """运行下载流程"""
        try:
            self.logger.info("开始下载流程")

            # 获取配置
            build_router_prefix = self.config_fetcher.fetch_config()

            # 过滤产品
            if products:
                build_router_prefix = {k: v for k, v in build_router_prefix.items() if k in products}

            total_files = 0
            total_resources = 0

            for product_key, router_path in build_router_prefix.items():
                self.logger.info(f"处理产品: {product_key}")

                # 解析路由路径
                product, dir_path = self.catalog_parser.parse_router_path(router_path)
                if not product or not dir_path:
                    self.logger.warning(f"无法解析路径: {router_path}")
                    continue

                # 获取目录结构
                catalog_data = self.catalog_parser.get_catalog(dir_path)
                if not catalog_data:
                    continue

                # 提取markdown文件
                markdown_files = self.catalog_parser.extract_markdown_files(catalog_data)
                self.logger.info(f"发现 {len(markdown_files)} 个MD文件")

                # 下载文件
                product_files, product_resources = self._download_product_files(
                    product, markdown_files, force_download
                )

                total_files += product_files
                total_resources += product_resources

            # 保存元数据
            self.metadata_manager.save_metadata()

            self.logger.info(f"下载完成: {total_files} 个文件, {total_resources} 个资源")

        except Exception as e:
            self.logger.error(f"下载流程失败: {e}")
            raise
        finally:
            self.http_client.close()

    def _download_product_files(self, product: str, markdown_files: List[Dict], force_download: bool) -> Tuple[int, int]:
        """下载指定产品的所有文件"""
        downloaded_files = 0
        downloaded_resources = 0

        for md_file in markdown_files:
            try:
                # 构建文件URL和本地路径
                # md_file['url'] 已经包含了完整的相对路径，直接使用
                file_url = f"{Config.BASE_URL}/{md_file['url']}"
                local_path = self._get_local_file_path(md_file['url'])

                # 下载MD文件
                if self.file_downloader.download_file(file_url, local_path, force_download):
                    downloaded_files += 1

                    # 下载资源文件
                    resource_count = self.resource_extractor.download_resources(local_path, file_url)
                    downloaded_resources += resource_count

                    if resource_count > 0:
                        self.logger.info(f"下载了 {resource_count} 个资源文件")

            except Exception as e:
                self.logger.error(f"处理文件失败 {md_file.get('url', '')}: {e}")

        return downloaded_files, downloaded_resources

    def _get_local_file_path(self, file_url: str) -> Path:
        """获取文件的本地存储路径"""
        # 移除开头的 "doc/assets/docs/"
        if file_url.startswith('doc/assets/docs/'):
            relative_path = file_url[len('doc/assets/docs/'):]
        else:
            relative_path = file_url

        return Path(Config.DOWNLOAD_DIR) / relative_path


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='MD文档批量下载器')
    parser.add_argument('--products', nargs='*', help='指定要下载的产品（不指定则下载所有）')
    parser.add_argument('--force', action='store_true', help='强制重新下载所有文件')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger('MDDownloader').setLevel(getattr(logging, args.log_level))

    # 创建下载管理器并运行
    manager = DownloadManager()
    manager.run(products=args.products, force_download=args.force)


if __name__ == "__main__":
    # 设置日志
    logger = Logger.setup_logger()
    logger.info("MD文档下载器启动")

    # 创建下载目录
    Path(Config.DOWNLOAD_DIR).mkdir(exist_ok=True)

    print("MD文档批量下载器")
    print("支持增量下载和资源文件自动提取")

    # 运行主函数
    main()
