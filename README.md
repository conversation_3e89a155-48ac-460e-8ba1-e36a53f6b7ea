# MD文档批量下载器

一个用于批量下载MD文档的Python工具，支持增量下载和资源文件自动提取。

## 功能特性

- ✅ **批量下载**: 自动获取所有产品的文档结构并批量下载
- ✅ **增量下载**: 通过比较文件修改时间，只下载有更新的文件
- ✅ **资源提取**: 自动提取MD文件中的图片等资源文件并下载
- ✅ **目录结构**: 保持原有的目录结构
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的下载日志
- ✅ **SSL忽略**: 自动忽略SSL证书验证

## 安装依赖

```bash
pip3 install requests
```

## 使用方法

### 基本用法

```bash
# 下载所有产品的文档
python3 md_downloader.py

# 下载指定产品的文档
python3 md_downloader.py --products tm gsc

# 强制重新下载所有文件
python3 md_downloader.py --force

# 设置日志级别
python3 md_downloader.py --log-level DEBUG
```

### 参数说明

- `--products`: 指定要下载的产品列表（不指定则下载所有产品）
- `--force`: 强制重新下载所有文件，忽略增量下载
- `--log-level`: 设置日志级别（DEBUG, INFO, WARNING, ERROR）

### 可用产品列表

根据配置文件，当前支持的产品包括：
- `tm` - 资金管理
- `gsc` - 共享服务中心
- `fssp` - 财务共享服务平台
- `pf` - 采购融资
- `fi` - 金融投资
- `eisi` - 企业智能服务平台
- `erm` - 企业风险管理
- `tax` - 税务管理
- `arap` - 应收应付
- `cb` - 现金银行
- `ma` - 管理会计
- `scm` - 供应链管理
- `pp` - 采购平台
- `qm` - 质量管理
- `eam` - 企业资产管理
- `ips` - 智能采购服务
- `ps` - 采购服务
- `ct` - 合同管理
- `ep` - 企业门户
- `crm` - 客户关系管理
- `mom` - 制造运营管理
- 以及其他产品...

## 文件结构

下载完成后，文件将按以下结构组织：

```
downloads/
├── metadata.json          # 文件元数据缓存（用于增量下载）
├── tm/                    # 产品目录
│   └── tm/
│       └── zh-Hans/       # 语言目录
│           ├── overview/  # 功能模块目录
│           │   ├── Product-Introduction.md
│           │   ├── Product-Value.md
│           │   └── images/
│           │       ├── diagram.png
│           │       └── ...
│           └── ...
└── logs/
    └── download.log       # 下载日志
```

## 增量下载原理

1. 使用HEAD请求获取远程文件的`Last-Modified`信息
2. 将文件元数据保存在`metadata.json`中
3. 下次下载时比较本地和远程的修改时间
4. 只下载有更新的文件，节省时间和带宽

## 资源文件提取

程序会自动分析MD文件内容，提取其中的资源引用：
- 图片引用：`![alt](path)` 和 `![](path)`
- 链接引用：`[text](path)`（排除外部链接）

资源文件会下载到相应的目录中，保持原有的相对路径结构。

## 日志文件

- 控制台输出：INFO级别及以上的日志
- 文件日志：`logs/download.log`，包含所有DEBUG级别的详细日志

## 测试

运行测试脚本验证功能：

```bash
python3 test_downloader.py
```

测试包括：
1. 配置获取测试
2. 目录解析测试
3. 完整下载测试（可选）

## 注意事项

1. **网络连接**: 需要能够访问 `https://open.inspures.com`
2. **SSL证书**: 程序会自动忽略SSL证书验证
3. **存储空间**: 确保有足够的磁盘空间存储文档和资源文件
4. **网络稳定**: 建议在网络稳定的环境下运行，程序有重试机制但网络中断可能影响下载

## 错误处理

- 网络错误：自动重试（最多3次）
- 文件不存在：记录警告并继续下载其他文件
- 权限错误：检查目录写入权限
- 磁盘空间不足：确保有足够的存储空间

## 示例输出

```
2025-08-04 11:18:20,434 - MDDownloader.ConfigFetcher - INFO - 获取配置: https://open.inspures.com/doc/assets/config/config.prod.json
2025-08-04 11:18:20,572 - MDDownloader.ConfigFetcher - INFO - 获取到 29 个产品配置
2025-08-04 11:18:20,573 - MDDownloader.DownloadManager - INFO - 处理产品: tm
2025-08-04 11:18:20,684 - MDDownloader.DownloadManager - INFO - 发现 5 个MD文件
2025-08-04 11:18:47,679 - MDDownloader.FileDownloader - INFO - 下载完成: downloads/tm/tm/zh-Hans/overview/Product-Introduction.md
2025-08-04 11:18:52,828 - MDDownloader.DownloadManager - INFO - 下载完成: 5 个文件, 13 个资源
```

## 许可证

本项目仅供学习和研究使用。
