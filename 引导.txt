请求头：
user-agent Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36

获取
https://open.inspures.com/doc/assets/config/config.prod.json

结果：
"buildRouterPrefix": {
    "index": "/doc/#/home",
    "inBuilderIndex":"/doc/#/inBuilder",
	"iGIX": "/doc/#/doc/md/iGIX%2FiGIX_2506%2Fdevelop%2Flowcode-dev%2Fdefault.md",
    "inIoT": "/doc/#/doc/md/inIoT%2FinIoT%2Fzh-Hans%2F01introduction%2Fintroduction.md",
    "cloud": "/doc/#/doc/md/cloud%2Fcloud%2Fdefault.md",
    "AI": "/doc/#/doc/md/AI%2Fcogservice%2Fdefault.md",
    "CSP": "/doc/#/doc/md/CSP%2FCSP%2F云服务支撑平台%2F安全搜索%2FWeb控制台使用指南.md",
    "inDataX": "/doc/#/doc/md/inDataX%2FinDataX%2Fzh-Hans%2Fgeneral_introduction.md",
    "gsc": "/doc/#/doc/md/gsc%2Fgsc%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "fssp": "/doc/#/doc/md/fssp%2Ffssp%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "pf": "/doc/#/doc/md/pf%2Fpf%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "tm": "/doc/#/doc/md/tm%2Ftm%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "fi": "/doc/#/doc/md/fi%2Ffi%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "eisi": "/doc/#/doc/md/eisi%2Feisi%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "erm": "/doc/#/doc/md/erm%2Ferm%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "tax": "/doc/#/doc/md/tax%2Ftax%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "arap": "/doc/#/doc/md/arap%2Farap%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "cb": "/doc/#/doc/md/cb%2Fcb%2Fzh-Hans%2F1-overview%2FProduct-Introduction.md",
    "ma": "/doc/#/doc/md/ma%2Fma%2Fzh-Hans%2F1-overview%2FProduct-Introduction.md",
    "scm": "/doc/#/doc/md/scm%2Fscm%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "pp": "/doc/#/doc/md/pp%2Fpp%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "qm": "/doc/#/doc/md/qm%2Fqm%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "eam": "/doc/#/doc/md/eam%2Feam%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "ips": "/doc/#/doc/md/ips%2Fips%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "ps": "/doc/#/doc/md/ps%2Fps%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "ct": "/doc/#/doc/md/ct%2Fct%2Fzh-Hans%2Fct-management%2Fbrief%2FProduct-Introduction.md",
    "ep": "/doc/#/doc/md/ep%2Fep%2Fzh-Hans%2Foverview%2FProduct-Introduction.md",
    "crm": "/doc/#/doc/md/crm%2Fcrm%2Fzh-Hans%2FCRM%2Foverview%2FProduct-Introduction.md",
    "mom": "/doc/#/doc/md/mom%2Fmom%2Fzh-Hans%2Foverview%2FProduct-Introduction.md"
  },

获取：
https://open.inspures.com/api/open/opendoc/v1.0/catalog?currentMenuPah=web/doc/assets/docs/tm/tm/zh-Hans

结果：
[
    {
        "name": "产品介绍",
        "originName": "overview",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/overview",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "产品简介",
                "originName": "Product-Introduction.md",
                "type": "markdown",
                "url": "doc/assets/docs/tm/tm/zh-Hans/overview/Product-Introduction.md",
                "expand": false,
                "isSelected": null,
                "submenu": null,
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 3,
                "fullyQualifiedName": null
            },
            {
                "name": "产品价值",
                "originName": "Product-Value.md",
                "type": "markdown",
                "url": "doc/assets/docs/tm/tm/zh-Hans/overview/Product-Value.md",
                "expand": false,
                "isSelected": null,
                "submenu": null,
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 3,
                "fullyQualifiedName": null
            },
            {
                "name": "应用场景",
                "originName": "Application-Scenarios.md",
                "type": "markdown",
                "url": "doc/assets/docs/tm/tm/zh-Hans/overview/Application-Scenarios.md",
                "expand": false,
                "isSelected": null,
                "submenu": null,
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 3,
                "fullyQualifiedName": null
            },
            {
                "name": "基础概念",
                "originName": "Basic-concept.md",
                "type": "markdown",
                "url": "doc/assets/docs/tm/tm/zh-Hans/overview/Basic-concept.md",
                "expand": false,
                "isSelected": null,
                "submenu": null,
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 3,
                "fullyQualifiedName": null
            },
            {
                "name": "新特性",
                "originName": "New-features.md",
                "type": "markdown",
                "url": "doc/assets/docs/tm/tm/zh-Hans/overview/New-features.md",
                "expand": false,
                "isSelected": null,
                "submenu": null,
                "description": null,
                "order": 5,
                "layer": 1,
                "priority": 3,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 1,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "账户管理",
        "originName": "account management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/account management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "账户全生命周期管理",
                "originName": "account lifecycle management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "账户开户",
                        "originName": "Account-Opening.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Account-Opening.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "账户变更",
                        "originName": "Account-Change.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Account-Change.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "账户冻结/解冻",
                        "originName": "account-freezing-and-unfreezing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/account-freezing-and-unfreezing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "账户销户",
                        "originName": "Account-closing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Account-closing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "账户延期",
                        "originName": "Account-Extension.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Account-Extension.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "账户年审",
                        "originName": "Account-annual-review.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Account-annual-review.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "虚拟子账户管理",
                        "originName": "Virtual-sub-account-management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Virtual-sub-account-management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "账户数量管理",
                        "originName": "Account-quantity-management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account lifecycle management/Account-quantity-management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "证书管理",
                "originName": "certificate Management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/account management/certificate Management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "证书过程管理",
                        "originName": "Certificate-Process-Management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/certificate Management/Certificate-Process-Management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "证书盘点",
                        "originName": "Certificate-Inventory.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/certificate Management/Certificate-Inventory.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "合作银行管理",
                "originName": "cooperative Bank Management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/account management/cooperative Bank Management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "合作银行准入",
                        "originName": "Access-to-cooperative-banks.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/cooperative Bank Management/Access-to-cooperative-banks.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "合作银行变更",
                        "originName": "Change-of-Cooperative-Bank.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/cooperative Bank Management/Change-of-Cooperative-Bank.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "账户常见问题解答",
                "originName": "account asked questions",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account asked questions",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "账户常见问题",
                        "originName": "Account-FAQ.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/account management/account asked questions/Account-FAQ.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 2,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "现金管理",
        "originName": "cash management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "企业收款业务",
                "originName": "enterprise collection business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise collection business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "收款登记与发布",
                        "originName": "Collection-Registration-and-Issuance.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise collection business/Collection-Registration-and-Issuance.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "企业付款业务",
                "originName": "enterprise payment business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise payment business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "主动付款场景",
                        "originName": "Active-payment-scenario.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise payment business/Active-payment-scenario.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "支付变更场景",
                        "originName": "Payment-change-scenario.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise payment business/Payment-change-scenario.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "被动扣款场景",
                        "originName": "Passive-Deduction-Scene.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise payment business/Passive-Deduction-Scene.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行代付场景",
                        "originName": "Bank-Payment-Scenario.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise payment business/Bank-Payment-Scenario.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "企业资金调度",
                        "originName": "Enterprise-fund-scheduling.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/enterprise payment business/Enterprise-fund-scheduling.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "日终期末业务",
                "originName": "day end and year-end business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/day end and year-end business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "日结",
                        "originName": "Daily-settlement.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/day end and year-end business/Daily-settlement.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "冲账",
                        "originName": "charge-off.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/day end and year-end business/charge-off.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企对账",
                        "originName": "Bank-Enterprise-Reconciliation.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/day end and year-end business/Bank-Enterprise-Reconciliation.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "常见问题",
                "originName": "frequently asked questions",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "现金管理常见问题解答",
                        "originName": "Cash-Management-FAQ.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Cash-Management-FAQ.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行代付常见问题解答",
                        "originName": "Frequently-Asked-Questions-about-Bank-Payment.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Frequently-Asked-Questions-about-Bank-Payment.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题1—单据流转配置说明",
                        "originName": "Topic 1-Document-Flow-Configuration-Instructions.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 1-Document-Flow-Configuration-Instructions.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题2—联动支付场景说明",
                        "originName": "Topic 2-Linked-Payment-Scenario-Description.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 2-Linked-Payment-Scenario-Description.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题3—批量调拨业务说明",
                        "originName": "Topic 3-Bulk-Transfer-Business-Description.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 3-Bulk-Transfer-Business-Description.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题4—集成智能审核说明",
                        "originName": "Topic 4-Integrated-Intelligent-Review-Explanation.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 4-Integrated-Intelligent-Review-Explanation.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题5—结算平台全流程查看说明",
                        "originName": "Topic 5-Full-Process-View-Explanation-for-the-Settlement-Platform.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 5-Full-Process-View-Explanation-for-the-Settlement-Platform.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题6—转账附言特殊字符说明",
                        "originName": "Topic 6-Special-Characters-in-Transfer-Remarks-Explanation.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 6-Special-Characters-in-Transfer-Remarks-Explanation.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题7—单位端结算单流程配置说明",
                        "originName": "Topic 7-Process-Configuration-Instructions-for-Entity-Side-Settlement-Documents.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 7-Process-Configuration-Instructions-for-Entity-Side-Settlement-Documents.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 9,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题8—支付变更配置说明",
                        "originName": "Topic 8-Payment-Change-Configuration-Instructions.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 8-Payment-Change-Configuration-Instructions.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 10,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题9—结算确认修改实际执行信息说明",
                        "originName": "Topic 9-Explanation-on-Modifying-Actual-Execution-Information-in-Settlement-Confirmation.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 9-Explanation-on-Modifying-Actual-Execution-Information-in-Settlement-Confirmation.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 11,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题10—打印配置说明",
                        "originName": "Topic 10-Print-Configuration-Instructions.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 10-Print-Configuration-Instructions.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 12,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题11—代付流程配置说明",
                        "originName": "Topic 11-Proxy-Payment-Process-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 11-Proxy-Payment-Process-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 13,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题12—银行代付新老功能切换说明",
                        "originName": "Topic 12-Switching-Design-of-Bank-Proxy-Payment-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 12-Switching-Design-of-Bank-Proxy-Payment-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 14,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题13—单位端自动入账配置说明",
                        "originName": "Topic 13-Entity-Side-Auto-Posting-Setup.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 13-Entity-Side-Auto-Posting-Setup.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 15,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专题14—结算平台支持批次开票说明",
                        "originName": "Topic 14-Explanation-of-Batch-Invoicing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/cash management/frequently asked questions/Topic 14-Explanation-of-Batch-Invoicing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 16,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 3,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "票据管理",
        "originName": "bill management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "票据业务类型",
                "originName": "bill business type",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill business type",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "票据种类",
                        "originName": "Bill-Type.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill business type/Bill-Type.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "应收票据",
                "originName": "bill receivable",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "票据签收",
                        "originName": "Bill-Signing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-Signing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据收票",
                        "originName": "Bill-Receipt.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-Receipt.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据背书",
                        "originName": "Bill-Endorsement.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-Endorsement.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据贴现",
                        "originName": "Bill-Discount.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-Discount.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据托收",
                        "originName": "Bill-Collection.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-Collection.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据质押",
                        "originName": "Bill-pledge.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-pledge.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "不得转让撤销",
                        "originName": "Bill-bdzrcx.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill-bdzrcx.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据保证",
                        "originName": "Bill_TMHPBSBZ.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill receivable/Bill_TMHPBSBZ.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "应付票据",
                "originName": "bill payable",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill payable",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "票据开票",
                        "originName": "Bill-invoicing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill payable/Bill-invoicing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "到期兑付",
                        "originName": "Payment-due.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill payable/Payment-due.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "应付票据变更",
                        "originName": "Change-in-notes-payable.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill payable/Change-in-notes-payable.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "电子债权凭证",
                "originName": "electronic debt certificate",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/electronic debt certificate",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "电子债权凭证",
                        "originName": "Electronic-Creditor-s-Rights-Document.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/electronic debt certificate/Electronic-Creditor-s-Rights-Document.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "供应链票据",
                "originName": "supply chain Bill",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/supply chain Bill",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "供应链票据应用",
                        "originName": "Application-of-Supply-Chain-Bills.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/supply chain Bill/Application-of-Supply-Chain-Bills.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 5,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "支票业务",
                "originName": "check business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/check business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "支票应用",
                        "originName": "Cheque-application.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/check business/Cheque-application.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 6,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "内部票据池",
                "originName": "internal bill pool",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "票据签收",
                        "originName": "Bill-Signing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Bill-Signing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收票登记",
                        "originName": "Ticket-Receipt-Registration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Ticket-Receipt-Registration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据开票",
                        "originName": "Bill-invoicing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Bill-invoicing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据背书",
                        "originName": "Bill-Endorsement.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Bill-Endorsement.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据贴现",
                        "originName": "Bill-Discount.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Bill-Discount.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据托收",
                        "originName": "Bill-Collection.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Bill-Collection.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据质押",
                        "originName": "Bill-pledge.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/internal bill pool/Bill-pledge.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 7,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "票据查询",
                "originName": "bill query",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill query",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "票据质押查询",
                        "originName": "Bill-PledgeQuery.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill query/Bill-PledgeQuery.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据解质押查询",
                        "originName": "Bill-RePledgeQuery.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill query/Bill-RePledgeQuery.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 8,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "票据接口",
                "originName": "bill api",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bill management/bill api",
                "expand": false,
                "isSelected": null,
                "submenu": [],
                "description": null,
                "order": 9,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 4,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "收付中心",
        "originName": "working capital management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "收款管理",
                "originName": "collection management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "经营性业务收款",
                        "originName": "Operating-business-receipts.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Operating-business-receipts.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "投融资类业务收款",
                        "originName": "Collection-of-investment-and-financing-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Collection-of-investment-and-financing-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收款后端扩展",
                        "originName": "Extended-Interface-Backend.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Extended-Interface-Backend.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收款相关前端脚本扩展方法",
                        "originName": "Cloud-Receipts-Related-Front-End-Script-Extension-Methods.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Cloud-Receipts-Related-Front-End-Script-Extension-Methods.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收款前端扩展",
                        "originName": "Extended-Interface-Front-End.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Extended-Interface-Front-End.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "业务收款常见问题解答",
                        "originName": "Business-Collection-Document-of-Receipt-and-Payment-Center.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Business-Collection-Document-of-Receipt-and-Payment-Center.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收款前端扩展说明",
                        "originName": "Collection-related-front-end-secondary-expansion-event-description.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Collection-related-front-end-secondary-expansion-event-description.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收款认领rpc服务文档说明",
                        "originName": "Collection-claim-rpc-service-documentation-description-.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Collection-claim-rpc-service-documentation-description-.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收款业务说明",
                        "originName": "Collection-Business-Description-Document.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/collection management/Collection-Business-Description-Document.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 9,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "付款管理",
                "originName": "payment management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "经营性业务付款",
                        "originName": "Operating-Business-Payments.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management/Operating-Business-Payments.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "投融资业务付款",
                        "originName": "Investment-and-financing-business-payments.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management/Investment-and-financing-business-payments.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "付款申请接口说明",
                        "originName": "Payment-Requisition-Interface-Description-Document.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management/Payment-Requisition-Interface-Description-Document.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "付款前端扩展说明",
                        "originName": "Business-payment-application-front-end-two-open-expansion.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management/Business-payment-application-front-end-two-open-expansion.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "对公付款常见问题解答",
                        "originName": "Frequently-Asked-Questions-on-Public-Payment-Continuously-Updated-.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management/Frequently-Asked-Questions-on-Public-Payment-Continuously-Updated-.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "付款录入、付款（扣款）认领说明文档",
                        "originName": "Payment-entry-payment-deduction-claim-documentation-.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/working capital management/payment management/Payment-entry-payment-deduction-claim-documentation-.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 5,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "资金集中",
        "originName": "concentration of funds",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "基础配置",
                "originName": "basic configuration",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/basic configuration",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "资金集中基础配置",
                        "originName": "Centralized-basic-allocation-of-funds.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/basic configuration/Centralized-basic-allocation-of-funds.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "内部账户",
                "originName": "internal account",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/internal account",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "内部账户开户",
                        "originName": "Open-an-internal-account.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/internal account/Open-an-internal-account.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部账户销户",
                        "originName": "Internal-account-closing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/internal account/Internal-account-closing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部账户变更",
                        "originName": "Internal-Account-Change.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/internal account/Internal-Account-Change.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部账户冻结解冻",
                        "originName": "Internal-account-freezing-and-unfreezing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/internal account/Internal-account-freezing-and-unfreezing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资金收付",
                "originName": "funds collection and payment",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds collection and payment",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "统收款业务",
                        "originName": "Collection-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds collection and payment/Collection-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "委托付款业务",
                        "originName": "Delegated-payment-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds collection and payment/Delegated-payment-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部转账业务",
                        "originName": "Internal-Transfer-Business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds collection and payment/Internal-Transfer-Business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资金调拨",
                "originName": "funds transfer",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds transfer",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "资金池调拨",
                        "originName": "Fund-Pool-Transfer.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds transfer/Fund-Pool-Transfer.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资金归集",
                "originName": "cash sweep",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/cash sweep",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "自动归集模式",
                        "originName": "automatic-collection-mode.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/cash sweep/automatic-collection-mode.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "手工归集模式",
                        "originName": "Manual-collection-mode.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/cash sweep/Manual-collection-mode.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 5,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资金下拨",
                "originName": "funds allocation",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds allocation",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "手动下拨模式",
                        "originName": "Manual-down-mode.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds allocation/Manual-down-mode.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "自动下拨模式",
                        "originName": "Auto-down-mode.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/funds allocation/Auto-down-mode.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 6,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "业务记账",
                "originName": "business accounting",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/business accounting",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "手工账务处理",
                        "originName": "Manual-accounting-processing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/business accounting/Manual-accounting-processing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "业务冲账",
                        "originName": "Business-charge-off.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/business accounting/Business-charge-off.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 7,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "日结与结息",
                "originName": "daily settlement and interest settlement",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/daily settlement and interest settlement",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "资金池日结",
                        "originName": "Daily-settlement-of-fund-pool.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/daily settlement and interest settlement/Daily-settlement-of-fund-pool.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部存款计息",
                        "originName": "Interest-accruing-on-internal-deposits.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/concentration of funds/daily settlement and interest settlement/Interest-accruing-on-internal-deposits.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 8,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 6,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "存款管理",
        "originName": "deposit management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "基础配置",
                "originName": "basic configuration",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/basic configuration",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "存款管理基础配置",
                        "originName": "Deposit-Management-Base-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/basic configuration/Deposit-Management-Base-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "银行存款业务",
                "originName": "bank deposit business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/bank deposit business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银行定期存款",
                        "originName": "bank-time-deposit.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/bank deposit business/bank-time-deposit.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行通知存款",
                        "originName": "Bank-call-deposit.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/bank deposit business/Bank-call-deposit.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行协定存款",
                        "originName": "Bank-Agreement-Deposits.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/bank deposit business/Bank-Agreement-Deposits.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行存款利息计提",
                        "originName": "Bank-deposit-interest-accrual.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/bank deposit business/Bank-deposit-interest-accrual.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "内部存款业务",
                "originName": "internal deposit business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/internal deposit business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "内部定期存款",
                        "originName": "Internal-Time-Deposit.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/internal deposit business/Internal-Time-Deposit.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部通知存款",
                        "originName": "internal-call-deposit.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/internal deposit business/internal-call-deposit.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部协定存款",
                        "originName": "Internal-Agreement-Deposits.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/internal deposit business/Internal-Agreement-Deposits.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部存款利息计提",
                        "originName": "Internal-deposit-interest-accrual.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/internal deposit business/Internal-deposit-interest-accrual.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "存款常见问题解答",
                "originName": "deposit asked questions",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/deposit asked questions",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "存款常见问题解答",
                        "originName": "Deposit-FAQ.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/deposit management/deposit asked questions/Deposit-FAQ.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 7,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "债务融资",
        "originName": "debt financing",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "授信管理",
                "originName": "credit management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/credit management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "授信额度申请",
                        "originName": "Credit-Line-Application.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/credit management/Credit-Line-Application.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "授信协议管理",
                        "originName": "Credit-agreement-management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/credit management/Credit-agreement-management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "授信分级分摊",
                        "originName": "Credit-Grading-Allocation.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/credit management/Credit-Grading-Allocation.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "授信与融资业务集成",
                        "originName": "Credit-and-financing-business-integration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/credit management/Credit-and-financing-business-integration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "授信手工执行",
                        "originName": "Manual-credit-execution.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/credit management/Manual-credit-execution.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "银行贷款",
                "originName": "bank loans",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "贷款合同初始",
                        "originName": "Initial-Loan-Contract.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Initial-Loan-Contract.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "贷款申请流程",
                        "originName": "Loan-Application-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Loan-Application-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "贷款合同管理",
                        "originName": "Loan-Contract-Management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Loan-Contract-Management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "借据管理",
                        "originName": "IOU-Management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/IOU-Management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "利息计提",
                        "originName": "Interest-Accrual.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Interest-Accrual.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "委托贷款",
                        "originName": "Entrusted-loan.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Entrusted-loan.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银团贷款",
                        "originName": "syndicated-loan.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/syndicated-loan.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "拼团贷款",
                        "originName": "Group-Loan.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Group-Loan.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "融资租赁",
                        "originName": "Finance-Leasing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Finance-Leasing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 9,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "贸易融资",
                        "originName": "Trade-Finance.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Trade-Finance.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 10,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "应收账款保理",
                        "originName": "Factoring-of-accounts-receivable.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/Factoring-of-accounts-receivable.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 11,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "统借统还模式",
                        "originName": "All-borrowing-and-return-mode.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bank loans/All-borrowing-and-return-mode.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 12,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "债券融资",
                "originName": "bond financing",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bond financing",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "债券注册业务",
                        "originName": "Bond-registration-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bond financing/Bond-registration-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "债券发行业务",
                        "originName": "Bond-issuance-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bond financing/Bond-issuance-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "债券还款",
                        "originName": "Repayment-of-bonds.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bond financing/Repayment-of-bonds.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "债券转股",
                        "originName": "Conversion-of-bonds.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bond financing/Conversion-of-bonds.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "利息计提",
                        "originName": "Interest-Accrual.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/bond financing/Interest-Accrual.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "内部贷款",
                "originName": "internal loans",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "内部贷款申请流程",
                        "originName": "Internal-Loan-Application-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans/Internal-Loan-Application-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部贷款合同管理流程",
                        "originName": "Internal-Loan-Contract-Management-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans/Internal-Loan-Contract-Management-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部贷款放还款流程",
                        "originName": "Internal-Loan-Repayment-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans/Internal-Loan-Repayment-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部贷款利息计提、划扣",
                        "originName": "Internal-loan-interest-accrual-deduction.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans/Internal-loan-interest-accrual-deduction.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部贷款利息计提合并冲账流程",
                        "originName": "Internal-Loan-InterestProvisionMergeStrikeBalance.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans/Internal-Loan-InterestProvisionMergeStrikeBalance.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部贷款业务受理/复核权限",
                        "originName": "Internal-loan-business-acceptance-review-authority.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/debt financing/internal loans/Internal-loan-business-acceptance-review-authority.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 8,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "借款与担保",
        "originName": "borrowing and guarantee",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "拆借管理",
                "originName": "borrowing management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/borrowing management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "拆借申请流程",
                        "originName": "Borrowing-Application-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/borrowing management/Borrowing-Application-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "拆借合同管理流程",
                        "originName": "Borrowing-Contract-Management-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/borrowing management/Borrowing-Contract-Management-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "利息计提",
                        "originName": "Interest-Accrual.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/borrowing management/Interest-Accrual.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "担保管理",
                "originName": "guarantee management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "担保合同申请流程",
                        "originName": "Guarantee-Contract-Application-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Guarantee-Contract-Application-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "担保合同管理流程",
                        "originName": "Guarantee-Contract-Management-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Guarantee-Contract-Management-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "担保与融资业务集成",
                        "originName": "Guarantee-and-financing-business-integration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Guarantee-and-financing-business-integration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "担保合同手工执行",
                        "originName": "Manual-execution-of-guarantee-contract.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Manual-execution-of-guarantee-contract.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "担保合同自定义导入",
                        "originName": "Custom-import-of-guarantee-contract.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Custom-import-of-guarantee-contract.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "抵质押合同申请流程",
                        "originName": "Pledge-Contract-Application-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Pledge-Contract-Application-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "抵质押合同管理流程",
                        "originName": "Mortgage-Contract-Management-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Mortgage-Contract-Management-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "抵质押与融资业务集成",
                        "originName": "Collateral-pledge-and-financing-business-integration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Collateral-pledge-and-financing-business-integration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "抵质押合同手工执行",
                        "originName": "Manual-execution-of-pledge-contract.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/borrowing and guarantee/guarantee management/Manual-execution-of-pledge-contract.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 9,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 9,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "信用证",
        "originName": "letter of credit",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "国际信用证",
                "originName": "international letter of credit",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit/international letter of credit",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "进口信用证开证",
                        "originName": "Import-Letter-of-Credit-Issuance.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit/international letter of credit/Import-Letter-of-Credit-Issuance.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "出口信用证收证",
                        "originName": "Export-letter-of-credit-receipt.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit/international letter of credit/Export-letter-of-credit-receipt.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "国内信用证",
                "originName": "domestic letter of credit",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit/domestic letter of credit",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "国内信用证开证业务",
                        "originName": "Domestic-letter-of-credit-issuing-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit/domestic letter of credit/Domestic-letter-of-credit-issuing-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "国内信用证收证业务",
                        "originName": "Domestic-Letter-of-Credit-Collection-Business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/letter of credit/domestic letter of credit/Domestic-Letter-of-Credit-Collection-Business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 10,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "资信管理",
        "originName": "credit management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "保证金业务",
                "originName": "margin business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/margin business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "支出保证金",
                        "originName": "Expenditure-margin.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/margin business/Expenditure-margin.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收到保证金",
                        "originName": "Deposit-received.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/margin business/Deposit-received.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资信证明业务",
                "originName": "credit certification business",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/credit certification business",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "资信证明开具",
                        "originName": "Credit-Certificate-Issuance.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/credit certification business/Credit-Certificate-Issuance.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "资信证明收取",
                        "originName": "Credit-Certificate-Collection.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/credit certification business/Credit-Certificate-Collection.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "保函业务",
                "originName": "cooperative bank management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/cooperative bank management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "开出保函",
                        "originName": "Issued-a-letter-of-guarantee.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/cooperative bank management/Issued-a-letter-of-guarantee.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "收到保函",
                        "originName": "receipt-of-guarantee.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/credit management/cooperative bank management/receipt-of-guarantee.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 11,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "资金风险管理",
        "originName": "financial risk management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "产品介绍",
                "originName": "overview",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management/overview",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "风险指标配置",
                        "originName": "Risk-Indicator-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management/overview/Risk-Indicator-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "风险监控配置",
                        "originName": "Risk-Monitoring-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management/overview/Risk-Monitoring-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "风险处置流程",
                        "originName": "Risk-Disposal-Process.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management/overview/Risk-Disposal-Process.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "开发指南",
                "originName": "developer's guide",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management/developer's guide",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "监控指标扩展开发指南",
                        "originName": "Monitoring-Indicator-Extension-Development-Guide.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/financial risk management/developer's guide/Monitoring-Indicator-Extension-Development-Guide.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 12,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "虚假贸易监控",
        "originName": "false trade monitoring",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "产品介绍",
                "originName": "overview",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring/overview",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "监控指标配置",
                        "originName": "Monitoring-Indicator-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring/overview/Monitoring-Indicator-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "监控模型配置",
                        "originName": "Monitoring-Model-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring/overview/Monitoring-Model-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "监控结果处置",
                        "originName": "Disposal-of-monitoring-results.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring/overview/Disposal-of-monitoring-results.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "开发指南",
                "originName": "developer's guide",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring/developer's guide",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "监控指标扩展开发指南",
                        "originName": "Monitoring-Indicator-Extension-Development-Guide.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/false trade monitoring/developer's guide/Monitoring-Indicator-Extension-Development-Guide.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 13,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "外汇管理",
        "originName": "exchange management",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "外汇牌价",
                "originName": "foreign exchange rate",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange rate",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "外汇牌价管理",
                        "originName": "Exchange-rate-management.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange rate/Exchange-rate-management.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "外汇即期",
                "originName": "foreign exchange spot",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange spot",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "外汇即期业务",
                        "originName": "foreign-exchange-spot-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange spot/foreign-exchange-spot-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "即期撤销挂单",
                        "originName": "Pendant-order-canceled-at-sight.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange spot/Pendant-order-canceled-at-sight.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "即期反向平仓",
                        "originName": "Spot-reverse-closing.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange spot/Spot-reverse-closing.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "外汇远期",
                "originName": "foreign exchange forward",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange forward",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "外汇远期业务",
                        "originName": "foreign-exchange-forward-business.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange forward/foreign-exchange-forward-business.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "外汇远期交割",
                        "originName": "Foreign-exchange-forward-delivery.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/exchange management/foreign exchange forward/Foreign-exchange-forward-delivery.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 14,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "营运沙盘",
        "originName": "operation sand table",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/operation sand table",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "营运沙盘",
                "originName": "Operating-sand-table.md",
                "type": "markdown",
                "url": "doc/assets/docs/tm/tm/zh-Hans/operation sand table/Operating-sand-table.md",
                "expand": false,
                "isSelected": null,
                "submenu": null,
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 3,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 15,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "银企服务",
        "originName": "bank corporate service",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "产品介绍",
                "originName": "intro",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/intro",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银企服务介绍",
                        "originName": "Introduction-to-bank-enterprise-services.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/intro/Introduction-to-bank-enterprise-services.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "常见问题",
                "originName": "faq",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/faq",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银行交易明细-操作文档",
                        "originName": "Bank-Transaction-Details-Operational-Document.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/faq/Bank-Transaction-Details-Operational-Document.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行对账单-操作文档",
                        "originName": "Bank-Statement-Operational-Document.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/faq/Bank-Statement-Operational-Document.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "非直联账户交易明细维护-操作文档",
                        "originName": "Maintenance-of-transaction-details-for-non-direct-linked-accounts.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/faq/Maintenance-of-transaction-details-for-non-direct-linked-accounts.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企调度-当日/历史明细查询",
                        "originName": "Bank-Enterprise-Scheduling-Daily-Historical-Detail-Query.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/faq/Bank-Enterprise-Scheduling-Daily-Historical-Detail-Query.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企余额对账单-操作文档",
                        "originName": "Bank-BalanceReconciliationStatement-Document.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank corporate service/faq/Bank-BalanceReconciliationStatement-Document.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 16,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "银企直联平台",
        "originName": "bank enterprise direct connection application",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "产品介绍",
                "originName": "intro",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/intro",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银企直联标准版",
                        "originName": "Bank-Enterprise-Direct-link-Standard-Edition.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/intro/Bank-Enterprise-Direct-link-Standard-Edition.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企直联高性能版",
                        "originName": "Bank-Enterprise-Direct-High-Performance-Edition.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/intro/Bank-Enterprise-Direct-High-Performance-Edition.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企直联信创版",
                        "originName": "Bank-enterprise-direct-link-letter-creation-version.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/intro/Bank-enterprise-direct-link-letter-creation-version.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "部署配置与运维",
                "originName": "deployment configuration and operations",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银企直联标准版安装及升级",
                        "originName": "Installation-and-Upgrade-of-Bank-Enterprise-Direct-Alliance-Standard-Edition.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/Installation-and-Upgrade-of-Bank-Enterprise-Direct-Alliance-Standard-Edition.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企直联高性能版部署配置",
                        "originName": "Bank-Enterprise-Direct-High-Performance-Edition-Deployment-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/Bank-Enterprise-Direct-High-Performance-Edition-Deployment-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企直联信创版系统安装及升级",
                        "originName": "Bank-enterprise-direct-link-letter-creation-version-system-deployment.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/Bank-enterprise-direct-link-letter-creation-version-system-deployment.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "浪潮GS银企直联云系统部署操作说明书-最简部署版",
                        "originName": "Langchao-GS-Bank-Enterprise-Direct-Cloud-System-Deployment-Operation-Manual-Simplest-Deployment-Version-v2025-02-06-1.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/Langchao-GS-Bank-Enterprise-Direct-Cloud-System-Deployment-Operation-Manual-Simplest-Deployment-Version-v2025-02-06-1.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "浪潮GS银企直联云系统部署操作说明书-标准部署版",
                        "originName": "Inspur-GS-Bank-Enterprise-Direct-Cloud-System-Deployment-Operation-Manual-Standard-Deployment-Version-v2025-02-06-1.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/Inspur-GS-Bank-Enterprise-Direct-Cloud-System-Deployment-Operation-Manual-Standard-Deployment-Version-v2025-02-06-1.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "配置说明-深圳金融电子结算中心",
                        "originName": "configuration-of-SHENZHEN-FINANCIAL-ELECTRONIC-SETTLEMENT-CENTER.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/configuration-of-SHENZHEN-FINANCIAL-ELECTRONIC-SETTLEMENT-CENTER.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "配置说明-****财务公司",
                        "originName": "configuration-of-Tongling-FINANCIAL-Company.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/deployment configuration and operations/configuration-of-Tongling-FINANCIAL-Company.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "常见问题",
                "originName": "faq",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银企直联常见问题",
                        "originName": "Bank-Enterprise-Direct-Joint-FAQs.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Bank-Enterprise-Direct-Joint-FAQs.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-付款问题",
                        "originName": "Specialized-O-M-Guidance-Payment-Issues.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Specialized-O-M-Guidance-Payment-Issues.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-明细问题",
                        "originName": "Specialized-O-M-Guidance-Detailed-Issues.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Specialized-O-M-Guidance-Detailed-Issues.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-电子回单",
                        "originName": "Special-Operation-and-Maintenance-Guidance-Electronic-Recall.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Special-Operation-and-Maintenance-Guidance-Electronic-Recall.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-工商银行-API版对接配置",
                        "originName": "Special-Operation-and-Maintenance-Guidance-ICBC-API-Docking-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Special-Operation-and-Maintenance-Guidance-ICBC-API-Docking-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-建设银行、国家开发银行、进出口银行-对接配置",
                        "originName": "Special-operation-and-maintenance-guidance-China-Construction-Bank-China-Development-Bank-Export-Import-Bank-docking-configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Special-operation-and-maintenance-guidance-China-Construction-Bank-China-Development-Bank-Export-Import-Bank-docking-configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-招商银行-CMB_API版对接配置",
                        "originName": "Special-Operation-and-Maintenance-Guidance-China-Merchants-Bank-CMB-API-Edition-Docking-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Special-Operation-and-Maintenance-Guidance-China-Merchants-Bank-CMB-API-Edition-Docking-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-浙商银行-API版配置",
                        "originName": "Special-Operation-and-Maintenance-Guidance-Zheshang-Bank-API-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Special-Operation-and-Maintenance-Guidance-Zheshang-Bank-API-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 8,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-花旗银行-对接配置",
                        "originName": "Specialized-O-M-Guidance-Citibank-Docking-Configuration.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Specialized-O-M-Guidance-Citibank-Docking-Configuration.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 9,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-账户附加信息",
                        "originName": "Specialized-O-M-Guidance-Additional-Account-Information.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Specialized-O-M-Guidance-Additional-Account-Information.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 10,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-招商银行-代发工资配置",
                        "originName": "Special-Operation-and-Maintenance-Guidance-China-Merchants-Bank-Payroll-Allocation.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Special-Operation-and-Maintenance-Guidance-China-Merchants-Bank-Payroll-Allocation.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 11,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "专门运维指导-资金调度程序配置实施专题",
                        "originName": "Bank-enterprise-direct-link-special-operation-and-maintenance-guidance-fund-scheduling-program-configuration-implementation-topic-.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Bank-enterprise-direct-link-special-operation-and-maintenance-guidance-fund-scheduling-program-configuration-implementation-topic-.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 12,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银企直联常用工具及问题介绍—GS7资金管理第二期",
                        "originName": "Introduction-to-Common-Tools-and-Problems-of-Direct-Joint-between-Bank-and-Enterprise-GS7-Fund-Management-Phase-II.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/Introduction-to-Common-Tools-and-Problems-of-Direct-Joint-between-Bank-and-Enterprise-GS7-Fund-Management-Phase-II.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 15,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "工行API已支持接口信息汇总",
                        "originName": "The-ICBC-API-has-supported-the-summary-of-interface-information-.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/faq/The-ICBC-API-has-supported-the-summary-of-interface-information-.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 17,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "开发相关",
                "originName": "develop",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/develop",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "银企直联标准版明细接口扩展个性化字段开发说明",
                        "originName": "Bank-Enterprise-Direct-Connection-Standard-Edition-Details-Interface-Extension-Personalization-Field-Development-Description.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/develop/Bank-Enterprise-Direct-Connection-Standard-Edition-Details-Interface-Extension-Personalization-Field-Development-Description.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "浪潮海岳银企直联云接入银行、财司、司库系统需求调研表",
                        "originName": "Langchao-Haiyue-Bank-Enterprise-Direct-Cloud-Access-Bank-Finance-Department-and-Treasurer-System-Demand-Survey-Form.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/develop/Langchao-Haiyue-Bank-Enterprise-Direct-Cloud-Access-Bank-Finance-Department-and-Treasurer-System-Demand-Survey-Form.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "浪潮海岳银企直联云接口测试要点清单",
                        "originName": "Langchao-Haiyue-Bank-Enterprise-Direct-Connection-Cloud-Interface-Test-List-V1-2-1.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/bank enterprise direct connection application/develop/Langchao-Haiyue-Bank-Enterprise-Direct-Connection-Cloud-Interface-Test-List-V1-2-1.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 17,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "资金监控",
        "originName": "fund monitoring",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/fund monitoring",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "余额类",
                "originName": "balance",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/fund monitoring/balance",
                "expand": false,
                "isSelected": null,
                "submenu": [],
                "description": null,
                "order": 1,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 18,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    },
    {
        "name": "开发指南",
        "originName": "develop",
        "type": "button",
        "url": "doc/assets/docs/tm/tm/zh-Hans/develop",
        "expand": false,
        "isSelected": null,
        "submenu": [
            {
                "name": "账户管理",
                "originName": "account management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "账户信息推送接口",
                        "originName": "AccountInterfaceMainTain.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management/AccountInterfaceMainTain.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "开户申请生单删除提交接口",
                        "originName": "BankAccOpeningDocMainTain.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management/BankAccOpeningDocMainTain.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "销户申请生单删除提交接口",
                        "originName": "BankAccClosingDocMainTain.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management/BankAccClosingDocMainTain.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "变更申请生单删除提交接口",
                        "originName": "BankAccChangeMainTain.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management/BankAccChangeMainTain.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "冻结申请生单接口",
                        "originName": "GenerateTmAccFrozenApply.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management/GenerateTmAccFrozenApply.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "解冻申请生单接口",
                        "originName": "GenerateAccountUnfreezeApply.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/account management/GenerateAccountUnfreezeApply.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 2,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "现金管理",
                "originName": "cash management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/cash management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "单位付款-服务",
                        "originName": "qyfk.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/cash management/qyfk.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "单位付款-扩展",
                        "originName": "qyfk extend.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/cash management/qyfk extend.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "企业收款",
                        "originName": "qysk.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/cash management/qysk.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行代付",
                        "originName": "yhdf.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/cash management/yhdf.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "结算平台",
                        "originName": "jspt.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/cash management/jspt.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 3,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "票据管理",
                "originName": "bill management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "应收票据",
                        "originName": "billreceivable.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billreceivable.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "应付票据",
                        "originName": "billpayable.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billpayable.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据背书",
                        "originName": "billendorse.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billendorse.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据贴现",
                        "originName": "billdiscount.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billdiscount.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据托收",
                        "originName": "billcollection.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billcollection.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据质押",
                        "originName": "billpledge.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billpledge.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 6,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据签收",
                        "originName": "billsigning.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bill management/billsigning.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 7,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 4,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "收付中心",
                "originName": "working capital management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "收款管理",
                        "originName": "collection management",
                        "type": "button",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/collection management",
                        "expand": false,
                        "isSelected": null,
                        "submenu": [
                            {
                                "name": "收款后端扩展",
                                "originName": "Extended-Interface-Backend.md",
                                "type": "markdown",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/collection management/Extended-Interface-Backend.md",
                                "expand": false,
                                "isSelected": null,
                                "submenu": null,
                                "description": null,
                                "order": 3,
                                "layer": 3,
                                "priority": 3,
                                "fullyQualifiedName": null
                            },
                            {
                                "name": "收款前端扩展",
                                "originName": "Extended-Interface-Front-End.md",
                                "type": "markdown",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/collection management/Extended-Interface-Front-End.md",
                                "expand": false,
                                "isSelected": null,
                                "submenu": null,
                                "description": null,
                                "order": 5,
                                "layer": 3,
                                "priority": 3,
                                "fullyQualifiedName": null
                            },
                            {
                                "name": "收款接口文档说明",
                                "originName": "Receiving-Interface-Description-Document.md",
                                "type": "markdown",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/collection management/Receiving-Interface-Description-Document.md",
                                "expand": false,
                                "isSelected": null,
                                "submenu": null,
                                "description": null,
                                "order": 8,
                                "layer": 3,
                                "priority": 3,
                                "fullyQualifiedName": null
                            }
                        ],
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 1,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "付款管理",
                        "originName": "payment management",
                        "type": "button",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/payment management",
                        "expand": false,
                        "isSelected": null,
                        "submenu": [
                            {
                                "name": "付款申请接口说明",
                                "originName": "Payment-Requisition-Interface-Description-Document.md",
                                "type": "markdown",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/payment management/Payment-Requisition-Interface-Description-Document.md",
                                "expand": false,
                                "isSelected": null,
                                "submenu": null,
                                "description": null,
                                "order": 3,
                                "layer": 3,
                                "priority": 3,
                                "fullyQualifiedName": null
                            },
                            {
                                "name": "付款认领RPC服务文档说明",
                                "originName": "Payment-Claim-RPC-Service-Documentation-Description.md",
                                "type": "markdown",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/working capital management/payment management/Payment-Claim-RPC-Service-Documentation-Description.md",
                                "expand": false,
                                "isSelected": null,
                                "submenu": null,
                                "description": null,
                                "order": 4,
                                "layer": 3,
                                "priority": 3,
                                "fullyQualifiedName": null
                            }
                        ],
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 1,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 5,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "存款管理",
                "originName": "deposit management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/deposit management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "存单信息推送",
                        "originName": "GenerateTmBankdeposit.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/deposit management/GenerateTmBankdeposit.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行存款申请单推送",
                        "originName": "GenerateBankDepositerQuest.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/deposit management/GenerateBankDepositerQuest.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "存单支取台账推送接口",
                        "originName": "GenerateTmBankdepositWDRLRecord.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/deposit management/GenerateTmBankdepositWDRLRecord.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "利息单信息推送",
                        "originName": "GenerateTmInterestDocInfo.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/deposit management/GenerateTmInterestDocInfo.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 7,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "债务融资",
                "originName": "debt financing",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/debt financing",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "授信管理",
                        "originName": "cred.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/debt financing/cred.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "担保管理",
                        "originName": "gm.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/debt financing/gm.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "银行借款",
                        "originName": "bl.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/debt financing/bl.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 3,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "债券融资",
                        "originName": "bis.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/debt financing/bis.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 4,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "内部贷款",
                        "originName": "il.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/debt financing/il.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 5,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 8,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资信管理",
                "originName": "credit management",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/credit management",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "资信管理RPC服务",
                        "originName": "Credit-Management-RPC-Service.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/credit management/Credit-Management-RPC-Service.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 11,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "银企服务",
                "originName": "bank corporate service",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "现汇业务",
                        "originName": "cash management",
                        "type": "button",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management",
                        "expand": false,
                        "isSelected": null,
                        "submenu": [
                            {
                                "name": "支付",
                                "originName": "pay",
                                "type": "button",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay",
                                "expand": false,
                                "isSelected": null,
                                "submenu": [
                                    {
                                        "name": "对外服务",
                                        "originName": "external services",
                                        "type": "button",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/external services",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": [
                                            {
                                                "name": "指令标记申请生单接口",
                                                "originName": "generate pay instructions apply.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/external services/generate pay instructions apply.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 1,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            }
                                        ],
                                        "description": null,
                                        "order": 1,
                                        "layer": 4,
                                        "priority": 1,
                                        "fullyQualifiedName": null
                                    },
                                    {
                                        "name": "二开扩展",
                                        "originName": "secondary development extension",
                                        "type": "button",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/secondary development extension",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": [
                                            {
                                                "name": "大额付款发送",
                                                "originName": "judgment for sending large payments.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/secondary development extension/judgment for sending large payments.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 1,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "更新付款状态",
                                                "originName": "update payment status.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/secondary development extension/update payment status.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 2,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "生成支付指令扩展",
                                                "originName": "generate payment instruction.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/secondary development extension/generate payment instruction.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 3,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "疑似重复付款检查",
                                                "originName": "suspected duplicate payment check.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/secondary development extension/suspected duplicate payment check.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 4,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "银行附件修改",
                                                "originName": "bank attachment modification.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/pay/secondary development extension/bank attachment modification.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 5,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            }
                                        ],
                                        "description": null,
                                        "order": 2,
                                        "layer": 4,
                                        "priority": 1,
                                        "fullyQualifiedName": null
                                    }
                                ],
                                "description": null,
                                "order": 1,
                                "layer": 3,
                                "priority": 1,
                                "fullyQualifiedName": null
                            },
                            {
                                "name": "银行代付",
                                "originName": "bankpay",
                                "type": "button",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/bankpay",
                                "expand": false,
                                "isSelected": null,
                                "submenu": [
                                    {
                                        "name": "银行代付",
                                        "originName": "xhyw.md",
                                        "type": "markdown",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/bankpay/xhyw.md",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": null,
                                        "description": null,
                                        "order": 1,
                                        "layer": 4,
                                        "priority": 3,
                                        "fullyQualifiedName": null
                                    }
                                ],
                                "description": null,
                                "order": 2,
                                "layer": 3,
                                "priority": 1,
                                "fullyQualifiedName": null
                            },
                            {
                                "name": "实时余额",
                                "originName": "balance",
                                "type": "button",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance",
                                "expand": false,
                                "isSelected": null,
                                "submenu": [
                                    {
                                        "name": "对外服务",
                                        "originName": "external services",
                                        "type": "button",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/external services",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": [
                                            {
                                                "name": "离线账户余额接入",
                                                "originName": "offline account balance access.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/external services/offline account balance access.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 1,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "在线余额查询",
                                                "originName": "online query.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/external services/online query.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 2,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            }
                                        ],
                                        "description": null,
                                        "order": 1,
                                        "layer": 4,
                                        "priority": 1,
                                        "fullyQualifiedName": null
                                    },
                                    {
                                        "name": "二开扩展",
                                        "originName": "secondary development extension",
                                        "type": "button",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/secondary development extension",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": [
                                            {
                                                "name": "查询发起前扩展",
                                                "originName": "before query initiation.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/secondary development extension/before query initiation.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 1,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "余额入库扩展",
                                                "originName": "Inbound.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/secondary development extension/Inbound.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 2,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "PostInfo扩展",
                                                "originName": "postinfo extension.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/secondary development extension/postinfo extension.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 3,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "非直联账户余额导入校验",
                                                "originName": "offline balance import valid.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/balance/secondary development extension/offline balance import valid.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 4,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            }
                                        ],
                                        "description": null,
                                        "order": 2,
                                        "layer": 4,
                                        "priority": 1,
                                        "fullyQualifiedName": null
                                    }
                                ],
                                "description": null,
                                "order": 3,
                                "layer": 3,
                                "priority": 1,
                                "fullyQualifiedName": null
                            },
                            {
                                "name": "交易明细",
                                "originName": "transdetail",
                                "type": "button",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail",
                                "expand": false,
                                "isSelected": null,
                                "submenu": [
                                    {
                                        "name": "对外服务",
                                        "originName": "external services",
                                        "type": "button",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/external services",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": [
                                            {
                                                "name": "银行交易明细对外服务",
                                                "originName": "External-service-of-bank-transaction-details.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/external services/External-service-of-bank-transaction-details.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 1,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "银行电子回单对外服务",
                                                "originName": "External-service-of-bank-lectronic-receipts.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/external services/External-service-of-bank-lectronic-receipts.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 2,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            }
                                        ],
                                        "description": null,
                                        "order": 1,
                                        "layer": 4,
                                        "priority": 1,
                                        "fullyQualifiedName": null
                                    },
                                    {
                                        "name": "二开扩展",
                                        "originName": "secondary development extension",
                                        "type": "button",
                                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/secondary development extension",
                                        "expand": false,
                                        "isSelected": null,
                                        "submenu": [
                                            {
                                                "name": "前端扩展",
                                                "originName": "Front-end-extension.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/secondary development extension/Front-end-extension.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 1,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "后端扩展",
                                                "originName": "Backend-extension.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/secondary development extension/Backend-extension.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 2,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            },
                                            {
                                                "name": "直联后端扩展",
                                                "originName": "Direct-connection-Backend-extension.md",
                                                "type": "markdown",
                                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/cash management/transdetail/secondary development extension/Direct-connection-Backend-extension.md",
                                                "expand": false,
                                                "isSelected": null,
                                                "submenu": null,
                                                "description": null,
                                                "order": 3,
                                                "layer": 5,
                                                "priority": 3,
                                                "fullyQualifiedName": null
                                            }
                                        ],
                                        "description": null,
                                        "order": 2,
                                        "layer": 4,
                                        "priority": 1,
                                        "fullyQualifiedName": null
                                    }
                                ],
                                "description": null,
                                "order": 4,
                                "layer": 3,
                                "priority": 1,
                                "fullyQualifiedName": null
                            }
                        ],
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 1,
                        "fullyQualifiedName": null
                    },
                    {
                        "name": "票据业务",
                        "originName": "bill business",
                        "type": "button",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/bill business",
                        "expand": false,
                        "isSelected": null,
                        "submenu": [
                            {
                                "name": "票据业务",
                                "originName": "pjyw.md",
                                "type": "markdown",
                                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/bank corporate service/bill business/pjyw.md",
                                "expand": false,
                                "isSelected": null,
                                "submenu": null,
                                "description": null,
                                "order": 1,
                                "layer": 3,
                                "priority": 3,
                                "fullyQualifiedName": null
                            }
                        ],
                        "description": null,
                        "order": 2,
                        "layer": 2,
                        "priority": 1,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 16,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            },
            {
                "name": "资金基础",
                "originName": "treasure foundation",
                "type": "button",
                "url": "doc/assets/docs/tm/tm/zh-Hans/develop/treasure foundation",
                "expand": false,
                "isSelected": null,
                "submenu": [
                    {
                        "name": "余额相关",
                        "originName": "balance about.md",
                        "type": "markdown",
                        "url": "doc/assets/docs/tm/tm/zh-Hans/develop/treasure foundation/balance about.md",
                        "expand": false,
                        "isSelected": null,
                        "submenu": null,
                        "description": null,
                        "order": 1,
                        "layer": 2,
                        "priority": 3,
                        "fullyQualifiedName": null
                    }
                ],
                "description": null,
                "order": 18,
                "layer": 1,
                "priority": 1,
                "fullyQualifiedName": null
            }
        ],
        "description": null,
        "order": 19,
        "layer": 0,
        "priority": 1,
        "fullyQualifiedName": null
    }
]



获取
https://open.inspures.com/doc/assets/docs/tm/tm/zh-Hans/overview/Product-Introduction.md

结果
md文件，内容：
## 产品简介

浪潮海岳司库管理构建“业务 + 财务”、“司库 + 财司”和“境内 + 境外”一体化、风险闭环的全球司库产品体系，基于交易层、配置层、决策与监管层三层架构，为企业提供境内外一体化司库、财务公司核心业务的系统支撑，直联外部金融机构及监管机构，并可实时获取工商大数据、行业大数据等信息，利用大数据分析平台进行智慧化运营，助力企业实现“业财资税融”全面一体化、数字化。

浪潮海岳司库管理以集团管控为重点，将精细化管控思想融合在业务处理流程中，通过建立资金管理中心与成员单位协同一体的管理体系，实现全集团银行账户、资金集中、收支结算、存款理财、债务融资、承兑票据、资金预算、资金风险的全面管理。通过互联企业采购、销售、报账、财务核算等业务，支持财务共享一体化应用，提升企业内部营运资金周转效率。

![](images/diagram.png)


获取

https://open.inspures.com/doc/assets/docs/tm/tm/zh-Hans/overview/images/diagram.png
资源图片
