#!/bin/bash
# MD文档批量下载脚本

echo "MD文档批量下载器"
echo "=================="

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3，请先安装Python3"
    exit 1
fi

# 检查requests库是否安装
python3 -c "import requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装requests库..."
    pip3 install requests
    if [ $? -ne 0 ]; then
        echo "错误: 安装requests库失败"
        exit 1
    fi
fi

# 显示使用帮助
show_help() {
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -p, --products      指定产品列表（用空格分隔）"
    echo "  -f, --force         强制重新下载所有文件"
    echo "  -l, --log-level     设置日志级别 (DEBUG|INFO|WARNING|ERROR)"
    echo "  -a, --all           下载所有产品"
    echo ""
    echo "示例:"
    echo "  $0 -p tm gsc       # 下载tm和gsc产品"
    echo "  $0 -a              # 下载所有产品"
    echo "  $0 -f -p tm        # 强制重新下载tm产品"
    echo ""
    echo "常用产品代码:"
    echo "  tm    - 资金管理"
    echo "  gsc   - 共享服务中心"
    echo "  fssp  - 财务共享服务平台"
    echo "  pf    - 采购融资"
    echo "  fi    - 金融投资"
    echo "  crm   - 客户关系管理"
    echo "  scm   - 供应链管理"
    echo "  更多产品请查看README.md"
}

# 解析命令行参数
PRODUCTS=""
FORCE=""
LOG_LEVEL="INFO"
ALL_PRODUCTS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--products)
            shift
            while [[ $# -gt 0 && ! $1 =~ ^- ]]; do
                PRODUCTS="$PRODUCTS $1"
                shift
            done
            ;;
        -f|--force)
            FORCE="--force"
            shift
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -a|--all)
            ALL_PRODUCTS="true"
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 构建命令
CMD="python3 md_downloader.py --log-level $LOG_LEVEL"

if [ -n "$FORCE" ]; then
    CMD="$CMD $FORCE"
fi

if [ -n "$ALL_PRODUCTS" ]; then
    echo "开始下载所有产品的文档..."
elif [ -n "$PRODUCTS" ]; then
    CMD="$CMD --products$PRODUCTS"
    echo "开始下载产品: $PRODUCTS"
else
    echo "请指定要下载的产品或使用 -a 下载所有产品"
    echo "使用 -h 查看帮助信息"
    exit 1
fi

# 创建下载目录
mkdir -p downloads logs

# 执行下载
echo "执行命令: $CMD"
echo ""
$CMD

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "下载完成！"
    echo "文件保存在 downloads/ 目录中"
    echo "日志文件: logs/download.log"
    
    # 显示下载统计
    if [ -d "downloads" ]; then
        MD_COUNT=$(find downloads -name "*.md" | wc -l)
        IMG_COUNT=$(find downloads -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" | wc -l)
        echo "统计: $MD_COUNT 个MD文件, $IMG_COUNT 个图片文件"
    fi
else
    echo ""
    echo "下载失败，请检查网络连接和日志文件"
    exit 1
fi
