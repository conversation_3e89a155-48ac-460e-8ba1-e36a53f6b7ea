# 收款登记与发布

## 1 业务概述

收款业务主要处理单位银行账户的收款，分为非共享模式和共享模式。

## 2 非共享模式

非共享模式下，不需要进行财务共享的基础配置，不需要将单据类型加入到单据类型分组中。但业务流程任然需要在【业务公共】-【共享服务】中配置，涉及外部流程节点![](images/0fa09242f686345fea8a56583f4b5665.png)的，需要在【流程平台】-【工作流平台】中定义审批流。

收款业务根据收款后是否需要认领，分为收款审核模式和收款认领模式。

### 2.1 收款审核模式

#### 2.1.1 业务概述

单位银行账户收款后，直接进行收款登记，提交审核，经审核后收款单据生效。

#### 2.1.2 相关配置

1）参数“收款启用发布认领”设置为“否”

功能路径：业务公共—业务配置—业务配置—全局参数配置

![](images/e1065b8ced87b28503e974ce7ce2bf38.png)

2）参数“收款自动审核”

若开启此参数，则收款登记提交后，自动完成审核。否则，需要人工审核。

![](images/8f533be681ce9b3a439098173826e316.png)

#### 2.1.3 业务流程

**1）收款登记**

功能路径：资金管理—现金管理—收款管理—收款登记

![](images/b91b34fe5bc856a18478bbeb74bfe45b.png)

打开功能菜单，界面上边为通过银企直联或手工维护导入的账户交易明细，勾选后，点登记，生成制单状态的收款单据，显示在界面下方。若无交易明细来源，可直接在界面下点“新增”按钮，手工录入收款单据。

收款单据保存后，提交审核。

**2）收款审核**

对收款单据进行审核。

功能路径：资金管理—现金管理—收款管理—收款审核

![](images/fe4c305557958247fdeeca09ac9da1c6.png)

### 2.2 收款认领模式

#### 2.1.1 业务概述

收款登记后需要发布认领，由财务或业务人员进行认领，认领时可关联合同、应收账款等业务信息，实现业财一体。

#### 2.1.2 相关配置

##### 2.1.2.1 启用参数“收款启用发布认领”

启用后，可引入银行流水，登记生成收款单。

功能路径：业务公共—业务配置—业务配置—全局参数配置

![](images/8e2f7c17e1256e9d26cc9ef7b25ff34b.png)

##### 2.1.2.2 收款单据类型定义

根据收款业务分类需要，定义不同的收款业务类型。每种收款业务类型都对应一类报账单，可通过关联表单模板进行扩展定义（下图中1，系统预置了一般收款、合同收款两类，可选择表单格式最相近的模板，在此基础上再进行扩展定义）。每种收款业务类型都关联具体的业务类型（下图中2，如经营性收款、票据托收、银行借款放款等等，根据需要进行关联）

功能路径：业务公共—收付中心—基础配置—业务收付类型

![](images/46859e2ed36637b0379afdb5ac5b7e02.png)

##### 2.1.2.3 定义报账单格式

可根据系统预制的单据模板，在此基础上按业务要求进行定制报账单，具体定制方法参见下面的业务示例

功能路径：业务公共—业务配置—业务配置—表单模板定制

![](images/5b8ad28c80ae865915772023bbbdf4dc.png)

##### 2.1.2.4 定义收款认领类型

功能路径：业务公共—收付中心—基础配置—资金认领类型定义

根据收款业务种类的不同，定义不同的收款认领类型，每种类型都需对应报账单据（即在上面2中定义的收款业务类型）

![](images/1a194d5afa9ff390b77e2c6903de4729.png)

##### 2.1.2.5 业务流程

收款单据流程即选择完认领类型后所生成的收款报账单提交时走的流程。

1）流程设计

功能路径：业务公共—共享服务—流程定义—公共流程设计

![](images/5e1816b2cbd2415786fe368eb0b3f540.png)

流程示例（根据业务场景需要，配置收款认领审批流程，非共享模式下不要加共享相关的操作节点）：

![](images/9339bd34a8e783b3376d7cbad16c9898.png)

2）流程分配，完善参与者等信息后，保存启用

功能路径：业务公共—共享服务—流程定义—公共流程分配

![](images/73a83a1b4e7a9a59e4253ad4b503f685.png)

![](images/49af81aa45f58829c597ac5059752d72.png)

#### 2.1.3 业务流程

1）收款登记，并发布认领

功能路径：资金管理—现金管理—收款管理—收款登记

![](images/025e13251e9764b7369f4282e6842555.png)

2）收款认领，选择认领类型，提交按配置的流程处理

功能路径：业务公共—收付中心—收款业务—收款认领

![](images/a650f9b94cf2e1fbf24d756bfee6cef5.png)

认领时，可关联合同、应收账款等业务信息，可关联资金计划。

![](images/f0ca9fdbd88a4c9d3b3aab8907c917dc.png)

### 2.3 收款明细自动发布认领

系统支持收款业务交易明细自动发布认领，即不需要引入银行交易流水登记收款单，由系统自动拉取银行交易流水（含银行直联账户交易明细、非直联账户导入的交易明细），自动发布到收款认领中。

具体配置如下：

1）参数配置

路径：业务公共—业务配置—业务配置—组织参数配置

![](images/86a6e4353a0e1d7d66fcf792aa6f8b20.png)

2）启用构件任务

路径：系统公共—系统配置—调度任务—任务管理

![](images/17a0f366e05343a469a4a54b5f0de2ae.png)

3）设置单据默认值

功能路径：资金管理—资金基础—系统设置—单据默认值设置

![](images/94f358b38e7eb673a649124185731cc3.png)

以上配置完成后，银行流水可实现自动发布。

## 3 共享模式

共享模式，即资金结算业务纳入财务共享中心处理，按照共享的流程、任务处理模式来管理。

### 3.1 业务概述

收款业务主要处理银行账户的收款，当前版本的收款业务主流程为：

1）财务人员登记收款结算单（引用银行流水或手工录入），并发布认领

2）财务人员、业务人员进行收款认领，选择认领类型后生成相应报账单

3）报账单提交，按照定义的流程进行处理。

![](images/13ef45654604f33acffe66048793fae8.png)

### 3.2 参数及相关配置

#### 3.2.1 启用参数“收款启用发布认领”

启用后，可引入银行流水，登记生成收款单。

功能路径：业务公共—业务配置—业务配置—全局参数配置

![](images/8e2f7c17e1256e9d26cc9ef7b25ff34b.png)

#### 3.2.2 收款单据类型定义

根据收款业务分类需要，定义不同的收款业务类型。每种收款业务类型都对应一类报账单，可通过关联表单模板进行扩展定义（系统预置了一般收款、合同收款两类，可选择表单格式最相近的模板，在此基础上再进行扩展定义）。每种收款业务类型都关联具体的业务类型（如经营性收款、票据托收、银行借款放款等等，根据需要进行关联）

功能路径：业务公共—收付中心—基础配置—业务收付类型

![](images/b3f78d7ad837f40685f7b33226d4e9d4.png)

#### 3.2.3 定义报账单格式

可根据系统预制的单据模板，在此基础上按业务要求进行定制报账单，具体定制方法参见下面的业务示例

功能路径：业务公共—业务配置—业务配置—表单模板定制

![](images/5b8ad28c80ae865915772023bbbdf4dc.png)

#### 3.2.4 定义收款认领类型

功能路径：业务公共—收付中心—基础配置—资金认领类型定义

根据收款业务种类的不同，定义不同的收款认领类型，每种类型都需对应报账单据（即在上面2中定义的收款业务类型）

![](images/863382ac184c0e3b549c9f8cf0b0c719.png)

#### 3.2.5 定义流程

收款认领需要配置收款报账单流程，即选择认领类型后，生成的收款报账单提交时走的流程，根据业务流程需要配置，示例如下：

![](images/312ee3698023106feb16950594dcaea1.png)

注意：共享模式参数需要选择“是”

![](images/57ea85387c2cbe9c76d7cd03e2b85b3d.png)

### 3.3 业务流程举例-合同收款

#### 3.3.1 基础配置

**1）定义收款类型**

路径：业务公共—收付中心—基础配置—业务收付类型

增加业务收款类型-合同收款，关联合同收款报账单模板，业务类型选择经营性收款

![](images/153a661bdb174d1c9536d02a83eb326c.png)

**2）定义合同收款报账单表单格式**

（合同收款单系统有预置，可直接使用；如有特殊要求，可按以下步骤基于预置的表单模板进行扩展）

路径：业务公共—业务配置—业务配置—表单模板定制

![](images/dbc385dfe218448cf3e26ff8ea659604.png)

保存后，左侧会显示出刚增加的“合同收款”表单，选中表单，点“表单设计”，打开表单设计界面。可以根据具体项目业务需求修改。表单格式定义完成，保存即可。

![](images/35e011e38a3f8a78e1805c57e221652c.png)

![](images/0e3e5e6be1b86d656c3c50a3740f319c.png)

**3）单据类型分组**

将新增的单据加入到单位类型分组中

路径：财务共享—运营支撑平台—服务定义—单据类型分组

![](images/e14ca647c0428d2011fd5e90dd6649a9.png)

**4）定义资金认领类型**

路径：业务公共—收付中心—基础配置—资金认领类型定义

新增资金认领类型，并关联前面定义的报账单

![](images/b5bf0037ebb99aee6e70e1413b9c86d7.png)

**5）定义业务流程**

路径：业务公共—共享服务—流程定义—公共流程设计/公共流程分配

根据实际的业务流程需要定义，示例：

![](images/47c0519cd1193e52d628a7fb81404e9b.png)

#### 3.3.2 业务流程

**1）登记并发布银行流水**

路径：资金管理—现金管理—收款管理—收款登记

财务人员勾选银行交易明细，点登记生成收款登记单；在收款登记单中勾选单据，点“发布”，将收款单发布认领。

如果是非直联账户，在收款登记单中，点“新增”手工录入收款单。

![](images/c7bb1351af05f8b5154565d381959871.png)

**注意**：勾选银行流水点登记时，需要设置单据默认值（功能路径：资金管理—资金基础—系统设置—单据默认值设置）

![](images/00cc07bdd4a9a4acfa0ec28619077af9.png)

**2）收款认领:**

路径：业务公共—收付中心—收款业务—收款认领

业务人员勾选待认领的单据，点“认领”按钮，选择认领类型，根据需要填写其他信息，点“确认”按钮，进入合同收款报账单

![](images/aa427a7e10aef4881f41a1b441ab8a23.png)

填写业务明细，选择对应合同，保存后提交，按照业务流程走后续环节

![](images/510c7f2f0fab8b4af079ce3d85f37942.png)

**3）提交**，按流程进行

**4）生成凭证**

按照凭证模板生成凭证（示例）

![](images/a0aca31d3c79d3cb4b5951e6e8a3b7a8.png)

![](images/19c95c59926ee2baa3b8f17d79c3fbd6.png)

![](images/946c233d6bc6dcc59e3f7f060f1c2d3a.png)

### 3.4 业务流程举例-与预算集成

实现对关键业务环节的实时控制，预算控制平台中需要设置以下主要功能：

#### 3.4.1 资金预算占用

业务收付类型推送。

![](images/03553d12b8b495c38a26c56018b5ea9a.png)

扩展表单对应的业务收付类型配置后，即可在业务对应定义中看到

业务对应定义截图：

![](images/dbd170d34b5e0855beda5ac28e4df4cc.png)

注：1、即使在业务对应定义中可以看到，也建议在配置预算前重新在业务收付类型进行推送

​		2、确认相应的业务收付类型跟表单模板定制中的扩展表单是同一个

相应配置如下：

![](images/32c8e4f3eac3a3e1e4d05395755eceb5.png)

![](images/4eac7005aa9194d5a400311a0b250873.png)

![](images/288ebec148108ad6c52561c272f6c7fb.png)

![](images/02a0dc8ceba30547d259da0a2d29cf5e.png)

![](images/e50377fc7e4a0ee7683342e7e7080417.png)

![](images/1f08651fd09f754fb14d2528a56757c1.png)

![](images/e56658c963c17f3f3ed0fddd1abec88e.png)

![](images/78be2303dd47166e3f037f6e06f282ef.png)

![](images/2569bd7e5906c119b0052e7c078df557.png)

若无相应字段，则在设置业务字段处引入即可：

![](images/c12eb633787ea852abcf17ed6a26188d.png)

公共流程分配中的流程配置

①配置一个占用节点（需在执行节点前）

![](images/3007a981ca64e05a867da3fecbd4dfe0.png)

②配置一个执行节点

![](images/ce96ac8f4d4d2e4845e4717a3e94aff8.png)

业务控制启用

![](images/277740c8e7b3faa423614b8f3d8d080d.png)

5、预算配置，图中全部按预算通用设置配置即可

![](images/4bf8aec4b1c8a8e9f5b8448ec0bb6ea2.png)

### 3.5 业务流程举例-与应收集成

业务收款可以实现与应收模块的集成，有全局参数配置控制与应收的集成与否。

![](images/192cbb79c008f56b98447d21ccc527c1.png)

还可以进核销应收的控制，当款项性质为应收类型时，控制保存时是否必须选择应收明细。当参数为不控制时，不校验。当参数为提示时，仅提示。当参数为禁止时，不允许保存单据。

![](images/6bcb9159b13295638a25fb55d5153fe4.png)

收付中心的一般收款、合同收款单将与应收集成iframe嵌入，所嵌入的基础表单为应收应付核销表单，项目可以在此扩展表单进行需求的个性化配置。

![](images/bf79d1b495927f677078f28b2ced544c.png)

回写条件：

参数启用启用与应收应付集成

主表或业务明细有款项性质

主表或业务明细有往来单位

有应收预制构件

### 3.6 业务流程举例-与信用集成

收付中心可以实现与信用模块的集成，有全局参数配置控制与信用的集成与否，启用后，业务收款单提交受信用额度控制。

![](images/e229e7e99cc4780c952917a7b24cf3ea.png)

### 3.7 业务流程举例-与合同集成

业务收款支持与合同的集成。基础表单设有合同收款单，项目使用合同集成的时候可以在合同收款单上做扩展表单。

![](images/c4b041d564ef99dae6f83f83db7b2472.png)

![](images/350ed850cbacb187a02f1436a5885628.png)

合同的帮助绑定的是业务支付申请_合同帮助，可以在合同台账进行预制数据。

合同名称按照收支属性和合同状态进行过滤显示，项目可以根据业务需求，在扩展表单进行过滤条件配置。只展示生效和执行状态的表单合同，起草、完成、作废、终止的默认不显示。

另外产品支持采购退款业务。是否采购退款标识可以项目在扩展表单放出。

![](images/e53a61e11c63f0a0e31fd424d770e932.png)

### 3.8 业务流程举例-与银行借款集成

业务收款支持与银行借款，借据的集成。在业务明细进行相关数据放出配置。

#### 3.8.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”或者“合同收款”，业务类型选择“银行借款放款”，“银行借款利息冲减”。

![](images/6e7ac4e8d9425030326520f8753fac4f.png)

#### 3.8.2 定义业务流程

同3.2.5章节配置

#### 3.8.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/d61c41590276d9adb06f7680d26b6b70.png)

将此类型对应的表单的借款相关字段放出来

![](images/a91ec2006397633ae4b6b1740d6137e1.png)

### 3.9 业务流程举例-与债券集成

业务收款支持与债券的集成。在业务明细进行相关数据放出配置。

#### 3.9.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”，业务类型选择“债券融资发行”

![](images/260b0fd21695982dfe9f5e52893a55fd.png)

#### 3.9.2 定义业务流程

同3.2.5章节配置

#### 3.9.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/8a3c4c1cedaf5198df5aea43cd68fe8a.png)

将此类型对应的表单的相关字段放出来

![](images/921459d9ab448c99223bc8ab52faa4b6.png)

### 3.10 业务流程举例-与保证金集成

业务收款支持与保证金的集成。

#### 3.10.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”，业务类型选择“支出保证金收回”

![](images/1c0a5a435a5c66373f3aa79f303a6eb1.png)

#### 3.10.2 定义业务流程

同3.2.5章节配置

#### 3.10.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/95a5135bee0359ca070827582af57d22.png)

将此类型对应的表单的相关字段放出来

![](images/e3aa2e60ebd65fb730665c6a6becf501.png)

### 3.11 业务流程举例-与保理集成

业务收款支持与保理的集成。

#### 3.11.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”，业务类型选择“应收账款保理放款”

![](images/2e9a301b573a4ccbe53e25e1a60983b5.png)

#### 3.11.2 定义业务流程

同3.2.5章节配置

#### 3.11.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/2b8f0bb778fde151c812fbf0e823a9da.png)

将此类型对应的表单的相关字段放出来

![](images/0f614d1367d552d2fe1d09041a0e621e.png)

### 3.12 业务流程举例-与信用证集成

业务收款支持与信用证的集成。

#### 3.12.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”，业务类型选择 “国内信用证收款”，“国内信用证贴现”，“出口信用证到期收款”，“国际出口信用证福费廷”。

![](images/6115df8f7c44ae87eae075fb4d9d12d0.png)

#### 3.12.2 定义业务流程

同3.2.5章节配置

#### 3.12.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/1b8fe2ba9b7545dec62167bdc4c6304d.png)

将此类型对应的表单的相关字段放出来

![](images/1b71f8f17c162fe187b4601e4e8a947d2.png)

### 3.13 业务收款支持与票据集成

#### 3.13.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”或“合同收款”，业务类型选择“票据托收”，“票据贴现”

![](images/2b9562534215e3e439d570dedbcd2207.png)

#### 3.13.2 定义业务流程

同3.2.5章节配置

#### 3.13.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/296dc7244d78e6375b7077d1da346159.png)

产品部有票据明细子表，业务收款票据明细表名BPBIZRCVBILLITEM。

![](images/6b7f7a4f0f33d6e85e66d4503e9d6ab3.png)

票据贴现

### 3.14 业务流程举例-与存款集成

业务收款支持与存款的集成。

#### 3.14.1 增加收款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般收款”，业务类型选择“银行定期存款解活”，“银行通知存款解活”。

![](images/d843d89a16b97fc29fe2d4eceeb024d4.png)

![](images/26b5319ada9682775fb08224db9d0ded.png)

#### 3.14.2 定义业务流程

同3.2.5章节配置

#### 3.14.3 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

定义对应收付类型的扩展表单

![](images/20bd236870404186fb335e1874d56311.png)

将此类型对应的表单的相关字段放出来

![](images/f946e3ac189ba3e8acbf6f46c958b3a1.png)

### 3.15 业务流程举例-红冲

目前产品支持的认领红冲场景:

经营性收款、支出保证金收回、银行借款放款，票据托收、票据贴现（供应链票据除外）、委托贷款借出还款、债券融资发行、银行定期存款解活如果单据已经认领完成后发现错误，可在收款认领功能联查认领记录，选中对应认领记录进行红冲处理。

![](images/bbb78861f07659a27a6000013957147d.png)

手工红冲

填负数金额的收款单，选红单标识和冲账报账单

![](images/7cbc8d2d52fcc2516640dbdd9f4a8f56.png)

补充说明：红冲会自动生成负数金额的红单，且不允许修改。

另外只有认领红冲的才会自动保存。如果没自动保存，请检查提示信息。红单需要走流程，和蓝单流程一样。如果想区分不同流程，通过 BPBUSINESSRECEIVING的ISREDBILL =1【是否红单】字段在流程中画分支, 红单走到凭证生成节点，生成凭证，会自动生成蓝单的负数冲销凭证。红单走完流程后，可重新在收款认领功能认领，或退回到收款登记后编辑、删除对应登记单

### 3.16 业务流程举例-非资金结算场景

共享模式下业务收款流程：

![](images/b0785049e36c5388a83a7195a5880f9c.png)

认领模式不支持非资金结算，手工发起的收款单需要挂下面的构件。

![](images/6dd582195d7e2bc5f4ca411edd3fcc2f.png)

![](images/47fad617af561c60d312a174cb4fcb82.png)

非共享模式下的流程：

![](images/a1c2331232f4fe0d67deb276ac551ff0.png)

非资金模式下的流程：

项目不上资金模块，做收款业务，从业务收款登记发起收款，不需要入资金账，可用于核销应收与合同业务。

![](images/98046e8b2d9777982595bfe241814cb9.png)

自处理构件选择业务收款非资金结算自处理。

![](images/47fad617af561c60d312a174cb4fcb82.png)

### 3.17 移动表单

![](images/7d518ab7860e2ca41332864532cad7c1.png)

#### 3.17.1 移动审批表单样式

![](images/7d4f7da438120f0a3e3ac39ee8629f4a.png)

#### 3.17.2 移动表单配置

移动审批表单可以通过下列方式配置：

IDP平台移动表单，idp开发移动表单后，使用这个联查菜单支持

![](images/ba5f41a48b43d2a7ab13ed64cf57582f.png)

#### 3.17.3 移动端子表模板设置

子表字段配置好后，选中扩展子表1，点击list全局模板；（如不配置，则在移动端不显示数据）

![](images/6364bba01e5b3b51a1cb20e77161680f.png)

在list全局模板中，首先点击选择模板，选择标准列表，点击确定。

![](images/1c0283f8a3742660af11a105e2b298e2.png)

在模板中，填写对应的扩展子表1的字段

![](images/ba5742b293eae355dc5ca8af2ebdbb3c.png)

#### 3.17.4 移动表单常用数据格式化方法

在移动表单的表格内容展示中存在金额，日期等字段因为数据库存值问题无法展示出，

金额用idp.utils.currency( , )方法,前面是行数据，后面是位数

日期用如下函数进行格式化

DATEFORMAT: function (value) {

value = value \|\| \`\`;

return value.replace(\` 00:00:00\`, \`\`);

},

#### 3.17.5 移动表单行点击事件

在移动表单展示的数据表中，通过vue修改前端的展示之后可以设计行点击事件，点击对应行，进而展示出该条数据的明细，而数据存在问题时，可以在该事件中写脚本自行配置，函数如下：

function(view,row,index){

//该函数获取row中的值，写入对应的明细中，如存在数据无法显示的情况可在此处配置逻辑显示

view.openDetail('TMZQCXSXX',row,index,'edit');//打开明细页面

}

![](images/18f33b7fd58ef11779133adf0c6caac9.png)

![](images/4196911ac876968e4194ad9e042d4dfa.png)

### 3.18 打印模板

【业务公共】-【业务配置】-【业务配置】-【打印格式定义】

![](images/7a66275f9e4e7072a1cbb2cafc865066.png)

![](images/84cb4d6aed4921c187bada838d21ad4b.png)

找到自己的业务种类，新增打印格式，在弹出的框中填写格式编号和名称。

点击格式数据源后的帮助绑定数据源

稍等加载，会出现之前推送的BE，选择好后确定。

![](images/f6d466c7191f8266c98fee0655b6c4d3.png)

然后开始设计打印格式

![](images/af14338649f04bc32252bdaa4f4fa6b9.png)

进入格式设计界面，点击左下角属性按钮进入属性界面，并将数据表拖拽到设计区

![](images/2d3609d4b784b695fdabbdc392abd1d3.png)

选择需要打印的字段，并勾选上页眉，确定上面的灰色Header表是页眉，下面蓝色Data表是数据源拉长数据源区域高度，将页眉中的字段名称与数据源中的字段数据一一对应排列在一起。字段名称左对齐，添加标题，居中等格式设置；

![](images/be78e4cd1374c7ad14b14d99ecf7bb30.png)

对于特殊格式的打印数据，根据需要设置其文本格式

![](images/92e4e3dd97877bc9d9611a05b2cb8d5d.png) ![](images/76a42f90a6c4b04b34afc9bc1d78ff2b.png)

对于枚举字段的打印，需要用到ParseEnumValue(String,String,Object)方法

![](images/cf4d941f1c6fb2f68fd7c631e2aa3092.png)

#### 3.18.1 打印常见问题总结

##### 3.18.1.1 打印提示没有打印格式

原因：项目扩展了多个单据，但是没有配置扩展单据对应的打印格式【目前业务收款单据的卡片打印是一对一的，比如项目扩展了初审、财务审核的扩展表单，那么单据在初审的时候打开卡片界面，就是打开的初审的表单，点击打印格式找的就是初审格式对应的打印格式，如项目上未配置，就会提示未找到打印格式】

业务收款单据，卡片打印的时候，与对应的扩展表单是一对一的关系。也就是说，如果项目上扩展了一个公有格式，一个制单格式，一个审核格式的表单，制单的扩展没有配置打印格式，但是配置了公有的打印格式，那么报账列表打印的时候，找不到制单格式的打印格式就会去找公有的打印格式，是能打印的，如果打开了卡片，是制单格式的，这个时候只会去找制单格式的打印，如果项目没配置就会提示找不到打印格式。

业务种类功能【业务公共--业务配置--业务配置--业务种类】新增元数据，新增的元数据搜索扩展的表单ID

![](images/40326e1611d8d1fc42233dbfa95323c1.png)

【有的项目在元数据中搜索不到自己扩展的表单ID，可能是因为没有推送BE，需在表单模板定制功能，找到扩展表单--实体维护，进入后生成BE，生成BE成功后再打开该功能新增元数据，即可找到项目上扩展表单ID】

##### 3.18.1.2 打印选不到字段

首先同步表单BE

![](images/7e4928f7dd0814c29e399e1130814d04.png)

然后再打印格式定义里面调整数据源，打开界面 点击新增字段

看能否选到

如果以上方法都不管用，有时候需要重新建立个打印格式解决。

##### 3.18.1.3 打印选不到数据源

![](images/3962330285264f924be19f5345b3e9af.png)

配置步骤：

业务种类功能【业务公共--业务配置--业务种类】新增元数据，新增的元数据搜索扩展的表单ID，在业务种类定义的元数据新增维护后可在业务种类中选到正常显示打印了 。有的项目在元数据中搜索不到自己扩展的表单ID，可能是因为没有推送BE，需在表单模板定制功能，找到扩展表单--实体维护，进入后生成BE

### 3.19 凭证模板

#### 3.19.1 凭证模板路径

【我的应用】-【财务会计】-【会计平台】-【公共凭证模板定义】

![](images/31f0270162ff5a677f1742b160d00b20.png)

![](images/9f9e87e17f24c0f0e3988bbd3d9a6642.png)

#### 3.19.2 凭证模板配置

![](images/6a312e2f349fbc8dc26508aaef4542f8.png)

