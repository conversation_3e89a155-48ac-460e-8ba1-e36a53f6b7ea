# 现金管理-结算平台常见问题解答

## 0.业务知识

### 0.1 划拨业务过程

#### （1）下拨业务流程图

![](images/image1.png)

#### （2）上划业务流程图

![](images/image2.png)

#### （3）拨付业务流程（同时记账）图

![](images/image3.png)

## 1.制单

### 1.1 选不到付款账户

#### （1）企业付款单

①账户的状态是否正常为启用，是否有停用、冻结等，是否已进行账户余额初始

②子账户的账户类型需为活期或保证金

③付款单位需在账户的授权使用范围内，且状态为启用

④若因业务需要，账户在资金组织中设置为了银行总户，需勾选【可作为备用金账户使用】，才能在企业付款时选择到。

⑤若单据上选择的结算方式类别为【转账】类，且结算方式的支付渠道只勾选银企直联，则账户只能选择开通银企直联支付的账户。

⑥检查扩展表单上是否有自定义的过滤条件。

⑦按上述步骤排查无效，跟踪sql反馈。

#### （2）付款录入

付款录入分为认领模式和非认领模式，过滤条件一般为如下所述，可先进行检查：

付款录入-认领模式：

1、账户状态为启用

2、账户种类为银行账户或现金账户

3、付款单位是账户的授权使用单位

付款录入-非认领模式：

1、账户状态为启用

2、账户种类为银行账户或现金账户，直联开通状态不能为 开通查询及支付

3、付款单位为账户授权使用单位

4、账户不能设置为资金中心总户

### 1.2 付款录入筛选不出交易明细

在银行交易明细功能，检查下述：

①银行交易明细状态需为【未生成】。

②银行交易明细收付属性需为【付款】。

③银行交易明细的业务参考号需为空。

④银行交易明细的业务类型需为【单位付款结算】或【资金池委托付款】或【定期存入】。

⑤检查付款录入的【资金行政组织数据权限】和【资金银行账户数据权限】。如果启用了，当前用户是否有权限。

⑥按上述步骤排查无效，跟踪sql反馈。

### 1.3 企业付款单从报账单字段带出规则

#### （1）转账附言

**①单条办理时：**

支付明细的转账附言（postscript）\>支付明细的摘要（summary）。

**②合并办理时：**

1.如果支付明细来源单据相同时，

1.1各支付明细（转账附言（postscript）\>摘要（summary））相同，取任一明细。

1.2各支付明细（转账附言（postscript）\>摘要（summary））不同，取支付主表的summary。

2.支付明细来源单据不同时，赋值为空。

参考SQL：
```sql
//支付明细
select postscript, summary
from BPBizPaymentReqReceiver
where parentid in (select id
from BPBizPaymentRequest
where srcdocno = '报账单编号'
)
//支付主表
select summary
from BPBizPaymentRequest
where srcdocno = '报账单编号'
```

#### （2）对方性质、对私账户

**①单条办理时：**

支付明细的对方性质（privateflag）：1 往来单位 ； 2
内部员工、3）专家库。是否对私账户（isprivateaccount）：1 是；0 否。

**②合并办理时：**

不会发生，系统有校验，不相同不允许合并办理。

参考SQL：
```sql
//支付明细
select privateflag, isprivateaccount
from BPBizPaymentReqReceiver
where parentid in (select id
from BPBizPaymentRequest
where srcdocno = '报账单编号'
)
```

#### （3）现金流量

**①单条办理时：**

支付明细的现金流量（cashflowitem）。

**②合并办理时：**

1.各支付明细现金流量相同时，取任一支付明细。

2.各支付明细现金流量不同时，不赋值。

参考SQL：
```sql
//支付明细
select cashflowitem
from BPBizPaymentReqReceiver
where parentid in (select id
from BPBizPaymentRequest
where srcdocno = '报账单编号'
)
```

#### （4）款项性质

注：结算办理的主体是支付明细，支付明细和业务明细无关联关系，所以无法取业务明细的款项性质，取的是支付主表的款项性质。

**①单条办理时：**

支付主表的款项性质（fundnature）。

**②合并办理时：**

1.各支付主表的款项性质相同时，取任一支付主表。

2.各支付主表的款项性质不同时，不赋值。

参考SQL：
```sql
//支付主表
select fundnature
from BPBizPaymentRequest
where srcdocno = '报账单编号'
```

#### （5）详细说明

**①单条办理时：**

支付明细的详细说明（description）。

**②合并办理时：**

取任一支付明细的详细说明。

参考SQL：
```sql
//支付明细
select description
from BPBizPaymentReqReceiver
where parentid in (select id
from BPBizPaymentRequest
where srcdocno = '报账单编号'
)
```

### 1.4 单位端单据摘要（转账附言）字典定义

基础数据-公共基础数据-常用代码-【标准代码】功能：

![descript](images/image4.png)

-   单位上划：单位资金上划摘要

-   单位下拨：单位下拨收款摘要

-   单位调拨：单位头寸调拨摘要

-   企业付款：单位付款结算摘要

### 1.5 保存提示TMPAYMENTSETTLEMENT：NUM01（自定义字段）

![descript](images/image5.png)

NUM数据库中为int类型，只能存储整数，金额字段请使用AMT类型扩展字段。

### 1.6 更改显示名后不起作用

扩展表单控件修改显示名后不起作用，**需要更改多语。**（控件名称一般为带label标识的）

![descript](images/image6.png)

![descript](images/image7.png)

### 1.7 业务事项关联的数据不存在，请检查数据来源

![descript](images/image8.png)

①检查表单上业务事项是否已停用。

②跟踪里搜索业务事项表（bfbusinessmatter）找到类似如下SQL，执行查找是否有数据。

```sql
select * from bfbusinessmatter where id = 'xx' and state_isenabled ='1'
```

### 1.8 核算组织必须为单位或者独立核算部门对应的核算组织

1、查询核算组织信息

```sql
//结算办理来源的企业付款单，查找来源报账单的业务支付申请主表上的核算组织
select ID,CODE,NAME_CHS from bfaccountingorganization where id=（select
accountingunit from BPBizPaymentRequest where srcdocno='报账单编号'）
```

2、【基础数据】-【公共基础数据】-【组织管理】-【核算组织】

①根据上述查出的核算组织编号和名称查找

②在核算组织功能检查核算组织与组织机构对应关系

![descript](images/image9.png)

### 1.9 账户余额相关问题

#### （1）获取账户余额

与全局参数配置-【资金管理】-【资金基础】-【直联账户余额获取途径】/【非直联账户余额获取途径】配置的是银行实时余额还是系统账面余额（根据需要配置取哪个）

![descript](images/image10.png)

![descript](images/image11.png)
#### （2）查询银行实时余额

银行余额在【业务公共】-【银企服务】-【现汇业务】-【账户余额查询】功能中查询

![descript](images/image12.png)

一、若该账户是非直联账户，目前有几种情况会更新：
1.【非直联账户余额导入】功能进行导入，但这个完全依赖导入的数据，即导入的哪一天就是哪一天，导入的余额是多少就是多少，不进行任何余额计算，也不进行补足；
2.【非直联账户余额维护】功能维护的余额也是完全依赖用户维护的数据，维护哪一天修改的就是哪一天，不会自动同步到下一天，即下一天的余额也需要用户自己维护。
3.使用【非直连账户余额弥补计划任务】来补足最新日期到当天的余额，但不会计算修改余额，只会补足最新日期到当天的记录。
作用：当日没有余额记录时，把前一天的余额同步到当日。
4.【银行交易明细】功能：根据参数【根据非直联交易明细自动更新余额】，通过导入银行交易明细间接更新非直联账户的当前余额，具体更新规则如下：
a.从银行余额表查询出该账户的交易流水最小日期之前的最大余额日期作为余额基准，从该日期开始逐日计算账户余额（计算规则：当日余额 = 昨日余额 + 当日交易明细汇总收入 – 当日交易明细汇总支出），直至当日
b.如果银行余额表无余额记录，则查找该账户余额的初始余额作为基准，从该日期开始逐日计算账户余额，直至当日

二、若该账户是直联账户，通过【银企-现汇-余额查询】自动任务构件获取。


#### （3）查询系统账面余额

账面余额在【资金管理】-【现金管理】-【查询】-【账户余额】功能中查询

![descript](images/image13.png)

通过在系统中做收付款业务单据，更新系统账面余额


### 1.10【银企直联】字段赋值说明

**①检查账户是否直联**

**②检查结算方式支付渠道是否勾选银企直联**

【银企直联】字段赋值规则：

爱学习课程讲解：资金结算银企直联付款规则培训<https://edu.inspur.com/#/study/course/detail/13&4c835acf-462e-43f7-a458-a709a486e7d4>

![descript](images/image14.png)

![descript](images/image15.png)

### 1.11 企业付款同城同行逻辑判断

参考论坛总结贴：<https://ilink.inspur.com/forum/1/question/223981>

![descript](images/image16.png)

![descript](images/image17.png)

### 1.12 单位端结算单和资金计划集成

目前支持的单据范围：企业付款单、单位下拨单、单位调拨单。具体单据类型如下：

企业付款单：编号：TM_QYFKD 名称：企业付款单

单位下拨单：编号：TM_DWXBD 名称：单位下拨单

单位调拨单：编号：TM_DWDBD 名称：单位调拨单

以企业付款单为例：

#### （1）配置扩展表单

表单模板定制，放出【资金计划】控件。

![descript](images/image18.png)

选中【资金计划】控件，对【隐藏】属性去掉勾选。

![descript](images/image19.png)

#### （2）计划控制设置

![descript](images/image20.png)

#### （3）计划控制启用

![descript](images/image21.png)

### 1.13 单位端付款结算单和预算集成

目前支持的单据范围：企业付款单、单位下拨单、单位调拨单、单位上划单。具体单据类型如下：

企业付款单：编号：PaymentSettlement
名称：转账付款单\--主动发起的企业付款申请单和付款录入非认领模式都用这个

单位下拨单：编号：TM_DWXBD 名称：单位下拨单

单位调拨单：编号：TM_DWDBD 名称：单位调拨单

单位上划单：编号：TM_DWSHD 名称：单位上划单

以下以企业付款单为例：

#### （1）【启用预算连用】参数

![descript](images/image22.png)

#### （2）业务对应定义

![descript](images/image23.png)

#### （3）业务流程设置

![descript](images/image24.png)

1、占用数/执行数控制以此处配置的为准，与共享流程节点上配置的无关。

2、操作名称【提交单据】：单据操作提交时机。操作名称【付款完成】：对于主动付款，是指单据操作结算确认功能的核销或直接确认；对于付款录入，是指单据操作结算复核功能的复核通过。

#### （4）业务控制启用

![descript](images/image25.png)

#### （5）其他

结算单据和预算的集成时机是通过在【业务流程设置】功能配置的方式，不支持在共享流程中设置的预算控制，如果配置了先将共享流程中配置的内容删除。

#### （6）控制不住原因排查

预算的维度和单据字段的关系，即业务对应未配置正确，尝试将下述【无对应检查】选项设置为【是】后，看是否能弹出预算提示，能弹出提示说明单据调用到了预算接口，需检查预算相关的配置。

![descript](images/image26.png)

#### （7）控制跟踪工具

【预算管理】-【预算控制】-【控制工具】-【控制跟踪工具】

![descript](images/image27.png)
### 1.14 帮助启用运行时定制

帮助使用运行时定制（以单位调拨单调出账户调整显示顺序为例）

#### （1）找到扩展表单上对应字段绑定的帮助字典

![descript](images/image28.png)

#### （2）对应帮助下增加一个全局扩展

![descript](images/image29.png)

#### （3）【列表取数】-【显示列】，选择字段/调整字段顺序，保存

（以把帮助的显示调整为账户名称、账号、银行为例）

![descript](images/image30.png)

![descript](images/image31.png)
#### （4）表单界面，选择相应帮助字段，勾选启用运行时

![descript](images/image32.png)

#### （5）效果

![descript](images/image33.png)

### 1.15 移动审批界面配置

单据适用范围：单位调拨、单位上划、单位下拨、企业付款

场景：默认展示的是基础表单的移动格式，和基础表单相同的话，无需配置。若项目需要显示一些自定义字段或者展示审批日志信息，可以配置扩展表单的移动表单，以单位调拨单为例：

#### （1）表单模板定制-扩展表单-移动设计

![descript](images/image34.png)

#### （2）展示自定义字段/展示审批日志

![descript](images/image35.png)

![descript](images/image36.png)

单据类型：

单位调拨：TM_DWDBD

单位上划：TM_DWSHD

单位下拨：TM_DWXBD

企业付款：TM_QYFKD

#### （3）效果

![descript](images/image37.png)

### 1.16 数字签名

#### 用户配置签名证书

功能路径：系统公共-系统管理-用户管理

![descript](images/image38.png)

#### 资金启用数字签名

功能路径：资金管理-资金基础-系统设置-【签名配置】功能，支持按照单据类型+
单据环节+单位为粒度配置，其中单位支持配置应用到所有单位。

> ![descript](images/image39.png)

注意：全局参数配置功能中，资金管理-现金管理-【启用系统证书控制】参数（和账户管理下的证书管理相关），跟数字签名没关系，不要混淆。

### 1.17 扩展表单界面展示审批日志

单位端的企业付款单、单位调拨单、单位上划单、单位下拨单适用。以企业付款单为例：

#### 定义扩展表单

![descript](images/image40.png)

拖出块布局区域：

![descript](images/image41.png)

拖出审批日志控件:

![descript](images/image42.png)
#### 效果

![descript](images/image43.png)

### 1.18 联查交易日志

使用场景：银企直联支付场景下，用户了解详细的支付情况。

#### 结算复核功能

![descript](images/image44.png)

#### 收付单据查询功能

![descript](images/image45.png)

### 1.19 专家库支付场景配置

报销单推送专家库信息到企业付款单结算，在表单模板定制功能，企业付款单卡片界面的扩展表单中需定制增加专家库信息。

1、【对方性质】枚举值列表中，增加枚举值3，专家库。

![descript](images/image134.png)

2、将【专家名称】控件，从隐藏区域拖出。

![descript](images/image135.png)

## 2.流程配置

### 2.1 企业付款单提交：未找到共享报账单，请检查是否共享单据

![descript](images/image46.png)

原因：单据的流程共享模式为【是】，但单据的应用为非共享模式场景。需将流程和单据的模式匹配起来。另外，项目上可能设置多个流程，可按优先级及流程的分配条件从上往下检查。

**非共享模式场景：**

1.  在【企业付款】功能，直接发起的企业付款单据。

2.  在/业务公共/收付中心/付款业务/【业务支付申请】功能，发起的业务报账单据，在结算办理功能，办理为的企业付款单据。

**共享模式场景：**

在/财务共享/网上报账服务/报账服务/【我要报账】功能，发起的业务报账单据，在结算办理功能，办理为的企业付款单据。

注意：从【业务支付申请】功能发起的，在【结算办理】办理生成的企业付款单为仍为非共享模式；从【我要报账】发起，在【结算办理】办理生成的才为共享模式。若项目上使用共享模式，建议为用户去掉【收付中心】-【付款业务】-【业务支付申请】的功能入口，统一从【我要报账】发起。

如不能确定制单入口，可查询下述SQL反馈：
```sql 
--结果为3代表从【我要报账】发起，1代表从【业务支付申请】功能发起
select GeneratedWay from BPBizPaymentRequest where docno ='报账单编号'
```
![descript](images/image47.png)

### 2.2 单据提交：未找到符合条件的流程定义

![descript](images/image48.png)

**（1）检查共享流程**

在/业务公共/共享服务/流程定义/流程分配功能中，检查单据的共享流程。

**（2）检查平台外部流程**

若1中流程首个节点是外部流程，在/流程平台/工作流平台/流程建模/流程分配功能中，定义平台的外部流程。

### 2.3 下一流程节点未找到待审批人，请检查流程配置

![descript](images/image49.png)

**（1）找到单据正在运行对应流程版本**

![descript](images/image50.png)

项目上可能设置了多个流程，系统分配时会按从上往下优先级查找（如不满足分配条件则找下一个，以此类推），可以上述查看流程【更多信息】中流程名称、编号、版本为准，可在流程历史版本找到对应的版本。

**（2）检查所提示的对应节点设置的参与者**

![descript](images/image51.png)

**（3）触发启动了新流程**

如果提示的信息不是流程上所配置的下一节点，则需要考虑此节点通过触发了新单据提交流程（如企业付款单结算复核通过生成委托付款单提交），需要检查另一个流程上对应的办理人。

### 2.4 查询BE异常，BE元数据不存在

![descript](images/image52.png)

**原因1：扩展表单被删除**

共享流程绑定的是扩展表单的实体，扩展表单被删除，又新建了扩展表单。

共享支持修改历史版本的实体，可尝试以下操作：

①找到对应的流程历史版本，**更换业务实体，**选择新的扩展表单。

②监听时有问题，对应历史版本**重新选择监听构件**。

③若①②操作后，流程通过时仍有此问题，检查对应版本流程，**下一节点的参与者设置及分配条件**，对涉及到取表单字段值的，重新选择。

### 2.5 单据提交异常，跟踪提示表达式解析异常

![descript](images/image53.png)

**（1）检查流程分配条件**

**（2）检查下一个节点（可能是流程启动后的第一个点）的参与者设置**

### 2.6 任务中心配置流程摘要

![descript](images/image54.png)

![descript](images/image55.png)

场景：项目上需要在用户的任务中心直接展示出账号、户名、付款金额、摘要等关键信息

配置方法：

①共享流程：业务公共/共享服务/公共流程分配找到对应的流程，设置流程摘要

![descript](images/image56.png)

②外部流程：流程平台/工作流平台/流程设计，设置流程摘要

![descript](images/image57.png)

配置格式可参考如下：

```sql
'我方户名：'+{Data:TMPAYMENTSETTLEMENT.PAYACCOUNTNAME}+'对方户名:'+{Data:TMPAYMENTSETTLEMENT.RECEIVINGACCOUNTNAME}+'摘要：'+{Data:TMPAYMENTSETTLEMENT.SUMMARY}
```

## 3.结算办理

### 3.1 单据在结算办理的待办看不到

#### 该用户不在【结算办理】节点的办理人列表中

-   检查流程分配的参与者

![descript](images/image58.png)

结算平台的节点【结算办理】、【结算复核】、【直联付款确认】、【结算确认】,一般仅需勾选【按对应功能的权限控制】即可，其他不用设置。

-   检查数据权限

![descript](images/image59.png)
资金中心端的单据适用【资金组织数据权限】，包含：资金上划、资金下拨、资金调拨、委托付款、内转付款、统管开票、统管背书。

单位端的单据适用【资金行政组织数据权限】，除上述枚举单据外的其他单据。

检查岗位授权的业务组织和岗位的数据权限
![descript](images/image60.png)
![descript](images/image61.png)

注意：在单据流转到【结算办理】节点前，就要给用户分配好权限。

若单据已流转到结算办理，又想变更办理人，可以：

1.  由能看到单据任务的用户在结算办理退回，流程再重新流转到结算办理节点,

2.  或在【流程控制台】功能中，找到单据，点击【重新生成任务】。

#### 报账单支付明细付款金额为0

比如有借款的场景，报销金额核销了借款金额，付款金额为0，报账单无需结算付款，则无需结算办理。流程会自动跳过【结算办理】节点，流转到下一节点。

![descript](images/image62.png)

#### 单据流转到了代付办理

一些报账单据，比如薪酬发放单，使用【代付办理】做办理。

原因是项目流程图规划不正确，此种情况应在流程图中画分支，不走【结算办理】节点。

使用资金管理---现金管理---查询\--【银行代付】功能中，按照单据编号查询，若能查到，则代表单据是要做代付办理。

#### 单据已被办理

报账单据已被用户办理为结算单，结算单流程还未结束前，报账单的流程会一直在【结算办理】节点，这是正常的。若报账单的支付明细为多条，则需多条支付明细对应的结算单流程都结束后，才会将报账单流程推送到下一节点。

![descript](images/image63.png)

### 3.2实际付款金额为0，流程仍显示在结算办理未自动通过

按关键字查看审计日志：构件执行返回9%单据内码%

```sql 
select \* from GSPAUDIT where DESCRIPTION like'构件执行返回9%单据内码%'
```

### 3.3终止办理

终止办理，即是终止付款的意思，终止办理后，如果报账单所有的支付明细均是最终状态（支付完成或终止办理），则会推动报账单的结算办理任务通过。

![descript](images/image64.png)

### 3.4结算单已结算完成，报账单在结算办理未流转到下一节点

![descript](images/image65.png)

（1）结算单状态是否为完成

（2）是否分拆办理，结算单金额与支付明细金额是否一致

（3）报账单是否有多条明细，多条明细是否全部为最终状态

（4）在待办事项中点通过（可跟踪sql）

### 3.5报账单结算办理提交结算单后，任务中心结算办理的待办不再显示

**使用场景：**报账单的所有支付明细结算单完成后，报账单的流程才会由结算办理节点流转至下一节点。一些项目要求结算单提交后，任务中心结算办理的待办消息提醒不再显示。

**补丁说明：**至少升级了Cloud60Brpc20231103DU、Cloud60FSPFHB20231030UJ补丁

**其他说明：**参数启用后，新做结算办理提交的单据才起作用。

**实现方式：**

\--增加暗参控制，默认不开启，项目根据需求启用，查看数据库表中记录：

```sql 
select \* from BFBFKVRESULT where id = 'Brpc_JSBLRequestTaskControl'
```
①如果没有下述参数，则插入：

```sql 
INSERT INTO BFBFKVRESULT (ID, CONFIGKEY, CONFIGVALUE) VALUES
('Brpc_JSBLRequestTaskControl', 'Brpc_JSBLRequestTaskControl','1');
```

②如果有下述参数，则更新：

```sql
update BFBFKVRESULT set CONFIGVALUE = '1' where id =
'Brpc_JSBLRequestTaskControl'
```

### 3.6扩展表单展示提醒信息

适用范围：结算办理、结算复核、直联付款确认、结算确认功能列表界面，以下以结算办理功能为例说明：

#### （1）扩展表单定义

![descript](images/image66.png)

增加块布局：

> ![descript](images/image67.png)
增加label控件：

> ![descript](images/image68.png)

#### （2）效果

![descript](images/image69.png)

### 3.7列表展示结算单的结算信息

某些项目结算办理允许修改结算方式、结算账户。结算单的结算方式、我方账户等信息和报账单不同，结算办理界面可在列表的齿轮图标中，勾选设置显示。

![descript](images/image70.png)

![descript](images/image71.png)

效果：

![descript](images/image72.png)

### 3.8列表数据展示方案

![descript](images/image73.png)

可配置列顺序、列排序等：

![descript](images/image74.png)

效果例如：按加急排序

![descript](images/image75.png)

### 3.9合并办理

![descript](images/image76.png)

## 4.结算复核

### 4.1结算复核隐藏【转线下付款完成】按钮（或其他）

①权限对象维护功能启用【资金结算平台按钮权限】权限字段。

②对应岗位分配数据权限时分配对应按钮。

![descript](images/image77.png)

![descript](images/image78.png)

### 4.2未生成内转而是流转到了委托付款

排查步骤：

①检查收款账户和付款账户，账户种类是否内部账户。

②检查收款账户和付款账户，是否为启用状态。

③检查收款单位，在往来单位\--内部单位是否勾选。

④检查收款单位，在往来单位银行账号---是否为此单据的收款账号。

⑤检查收款账户对应的往来单位是不是有多个。

![descript](images/image79.png)

进一步分析：

委托付款单退回，企业付款单退回到结算办理，保存时跟踪SQL，排查paymenttype赋值
```sql 
//查询企业付款单付款类型(5代表内转)
Select PaymentType from TMPaymentSettlement where docno =
'结算单编号';
```
常见原因：

①收款账号有对应的非内部往来单位（上述第③条）。

②具体排查可按下述步骤
```sql 
1、--【前提】付款账户是内部户(下面sql有结果）

Select BFBankAccounts.Id,BFBankAccounts.InnerOrOuter From BFBankAccounts
Where BFBankAccounts.Id =(select payAccount from tmpaymentsettlement
where docno=\'企业付款单号\' And BFBankAccounts.AccountStatus = 2 and
BFBankAccounts.InnerOrOuter = 1

2、--查询收款账号是否内部户

Select BFBankAccounts.Id,BFBankAccounts.InnerOrOuter From BFBankAccounts
Where BFBankAccounts.AccountNo = \'收款账号\' And
BFBankAccounts.AccountStatus = 2

3、--如果第2步InnerOrOuter 不是1，为委托付款，如果是1，继续往下

4、--查询收款账号对应的往来单位是否为内部单位

select Partner.InteriorAdminOrg,Partner.InteriorCompany,BankAccounts.ID
as AccountID from bfPartner Partner left join bfAdminOrganization
AdminOrganization on Partner.InteriorAdminOrg=AdminOrganization.ID

left join bfPartnerBankAccounts PartnerBankAccounts on
PartnerBankAccounts.PartnerID = Partner.ID

left join bfBankAccounts BankAccounts on BankAccounts.AccountNo =
PartnerBankAccounts.AccountCode

left join bfAdminOrganization AdminOrganization2 on
BankAccounts.OpenAccountUnit=AdminOrganization2.ID

where PartnerBankAccounts.AccountCode='收款账号' and
BankAccounts.AccountStatus=2 and Partner.state_isenabled='1' and
PartnerBankAccounts.accountstate='0'

5、--如果第4步有结果，且InteriorCompany为1，为内转付款，否则为委托付款。
```

### 4.3结算平台功能列表支持显示自定义列

适用范围：结算办理、结算复核、直联付款确认、结算确认功能列表界面，以下以结算复核功能为例说明：

#### （1）打开扩展表单，选中列表

![descript](images/image80.png)

#### （2）将列双击选到右侧

![descript](images/image81.png)

#### （3）效果

![descript](images/image82.png)

### 4.4结算平台功能列表支持筛选自定义列

适用范围：结算办理、结算复核、直联付款确认、结算确认功能列表界面，以下以结算复核功能为例说明：

#### （1）定义扩展表单

![descript](images/image83.png)

#### （2）将控件拖拽到筛选区域

![descript](images/image84.png)

#### （3）修改显示名和过滤属性

![descript](images/image85.png)

![descript](images/image86.png)

#### （4）效果

![descript](images/image87.png)

![descript](images/image88.png)

### 4.5付款结算启用密码确认

场景：在结算办理、结算复核关键操作时做二次密码确认，为资金支付过程加一层安全保障。

#### 全局参数配置

资金管理-资金基础-【付款结算启用密码确认】

![descript](images/image89.png)

#### 用户设置独立密码

![descript](images/image90.png)

#### 效果

在弹出的密码框中，录入第2步设置的独立密码：

![descript](images/image91.png)

## 5.结算确认

### 5.1结算确认找不到单据

现象：在任务中心能看到任务在结算确认，在结算确认功能点进去看不见。（结算确认列表找不到）

**①检查流程在结算确认点。**

**②单据卡片状态是否是 银行付款成功。**

**③功能标识**。

原因：流程配置问题，按上述①②③检查后反馈。
```sql 
\--查询功能标识
select gnbs from tmjsxx where jsdbh='结算单编号'

\--查询单据状态
select docstatus from TMPaymentSettlement where docno='结算单编号'
```
### 5.2结算确认手工核销获取不到银行交易明细

![descript](images/image92.png)

结算确认手工单据匹配交易明细规则：

**（1）单据有业务参考号时**

①先根据业务参考号、付款账号、币种、金额过滤。

②如果没有匹配到，根据付款账号、币种、金额、收款账号、收款户名过滤。

**（2）单据没有业务参考号时**

根据付款账号、币种、金额、收款账号、收款户名过滤。

**（3）勾选单据时跟踪，搜索表BPBANKTRANSCATIONDETAILS，执行对应sql排查。**

### 5.3结算确认银行交易明细区域配置

场景：①结算确认支持匹配收款账号、收款户名不同交易明细。

②结算确认支持匹配含收款账号为空、收款户名为空的交易明细。

③结算确认支持一对多、多对一核销。

配置方法：

【表单模板定制】-【结算确认】-【列表设计】，对应控件取消隐藏。

![descript](images/image93.png)

![descript](images/image94.png)

### 5.4资金上划单结算确认提示获取不到默认值

资金池上划单在结算最后一步生成单位上划单的提示单据未设置默认值，可在【单据默认值设置】设置单位端单据的默认制单人。

![descript](images/image95.png)

### 5.5任务不存在，请刷新后重试

现象：通常出现在结算的最后一步（结算确认/中心办理监听等），在推动报账单至下一环节时提示的。

![descript](images/image96.png)

![descript](images/image97.png)

原因：通常是岗位权限调整导致的。在流程控制台检查报账单在结算办理节点的办理人，确认企业付款单的办理人是否在此列表中。

操作：**在流程控制台对报账单重新生成任务。**

（另：如果在其他节点有此提示，也可尝试在流程控制台重新生成结算单任务）

### 5.6单位调拨单\--调拨付款入账自动生成调拨收款单问题

单位调拨单在入账时机是否会自动生成相应的收款单：

与【全局参数配置】-【资金管理】-【现金管理】-【调拨成功即确认收款】的参数有关

![descript](images/image98.png)

若参数不勾选，则不自动生单；若参数勾选，根据调拨单的关键信息查找系统里是否有对应收款流水

    a)  未查找到\--生成单据

    b)  查找到流水状态为【未生成】\--生成单据并与流水核销

    c)  查找到流水状态为【已生成】/【已核销】\--系统认为收款流水已入账，不再生成单据

### 5.7资金中心下拨单结算确认提示流程相关问题

Q1：单据数据不存在，请检查请检查是否尚未保存或已删除

Q2：未找到共享报账单/当前单据为共享模式，请在流程分配中将该类单据对应的流程的"共享模式"属性修改为是后再提交

Q3：跟踪里提示 流程启动异常

排查方法：检查单位下拨单的流程设置，一般为单位下拨单的流程没有设置正确。注意要区分设置好由资金中心发起资金下拨单核销时生成的单位下拨单和由单位端主动发起的单位下拨单。

## 6.银行付款监听

### 6.1流程一直卡在银行付款结果监听环节

![descript](images/image99.png)

（1）流程控制台查看是否为异常流程，可查看异常信息。

（2）检查单据流程配置，监听节点挂载构件是否正常。

（3）单据状态为：【银行付款成功】或者【银行付款失败】

操作：手工执行下监听

①无法驱动，执行时跟踪sql，定位问题或反馈。

②能正常驱动，个别单据非普遍现象，手工恢复即可。

③能正常驱动，普遍现象。需要日志分析。

另外：检查平台【任务管理】功能中，名称为 流程监听节点触发器
的构件是否为启动状。如果上述步骤状态为启动状态，点击右上角【日志联查】查看近期是否执行过计划任务，如果上次执行时间和配置不一致，则考虑是否项目是否已经启动了计划任务。

（4）单据状态为：【等待发送银行】或者【银行正在处理】

参考银企服务相关调度任务章节

## 7.凭证、电子影像

### 7.1单位调拨单显示联查凭证按钮

场景：

①先核算后结算，出纳在结算办理需要查看凭证，在单据的卡片界面展示【联查凭证】按钮。

②在凭证生成后加财务审核/财务复核等共享的节点，需要在办理时单据的卡片界面展示【联查凭证】按钮。

配置方法：共享流程里对应节点配置及效果如下图（如果是在流程中的单据需要编辑历史版本流程），并且需要在对应的节点点【办理】后才能展示。

![descript](images/image100.png)

![descript](images/image101.png)
### 7.2报账单（上游单据）关联查看企业付款单（下游单据）的影像

在上游单据，例如报销单、业务支付申请单等单据上，关联查看企业付款单的影像（一般是银行回单）。

（1）影像约束功能-配置上游单据的影像关联配置![descript](images/image102.png)

（2）效果：

![descript](images/image103.png)

![descript](images/image104.png)

### 7.3企业付款单（下游单据）关联查看报账单（上游单据）的影像

在下游单据企业付款单，关联查看例如报销单、业务支付申请单等单据的影像（一般是发票等）。

（1）影像约束功能-配置下游单据的影像关联配置：

![descript](images/image105.png)

（2）效果：

![descript](images/image106.png)

![descript](images/image107.png)

## 8.自动任务

### 8.1单位付款自动核销计划任务

#### 自动核销规则

处理业务单据包括：在系统发起的企业付款单、单位上划单、单位调拨单。

该计划任务会处理直联付款及线下付款的单据，直联付款一般都有业务参考号，线下付款单据没有业务参考号，但只要按以下匹配规则能匹配到流水，就会自动核销。

自动核销规则：

（1）如果有业务参考号

①先根据业务参考号、付款账号、币种、金额进行核销

②如果没有匹配到，根据关键要素：付款账号、收款账号、银行处理日期、币种、金额、摘要，且交易明细的业务参考号为空
进行核销

（2）如果没有业务参考号

根据关键要素：付款账号、收款账号、银行处理日期、币种、金额、摘要，且交易明细的业务参考号为空
进行核销

**注：**线下付款单据没有银行处理日期时，使用复核日期匹配，单据的复核日期需**大于等于**流水的银行处理日期

#### 不能自动核销排查步骤

（1）检查单据状态需为【银行付款成功】，这种状态的才在自动核销扫描范围内。

（2）单据上的申请日期（requestdate）需要晚于当前（构件执行时间）向前backDays，

backDays的值可在对象参数查看，默认为31天。

若需要修改对象参数的值，可以将调度任务改为非系统预置，然后在前台修改backDays

```sql 
select ispreset from GSPSCHEDULERJOB where
NAME_CHS='单位付款自动核销计划任务'
--把ispreset字段改成0代表非系统预置；
update gspschedulerjob set ispreset = '0' where name_chs = '单位付款自动核销计划任务';
```

（3）关键要素不匹配

**无法自动核销排查sql**

```sql
--付款单
select id,
docstatus,isbankcommpay,BusinessRefNo,payaccount,payaccountno,receivingaccountno,currency,paymentamount,summary,bankhandletime,banksenttime,reviewtime,ReceiptID
from tmpaymentsettlement where docno='付款单号';
--交易明细
select id,transstatus as docstatus,'' as
isbankcommpay,BusinessRefNo,bankaccount as payaccount, bankaccountno as
payaccountno,reciprocalaccountno as
receivingaccountno,currency,settlementamount as
paymentamount,summary,transactiondate as bankhandletime,transactiontime
as banksenttime, null as reviewtime,BankReceiptID from
bpbanktranscationdetails where bankflowno='银行流水号' and
incomeorexpenditure=1;
```

 其中：
docstatus单据状态
isbankcommpay是否银企直联
BusinessRefNo业务参考号
payaccount付款账户
payaccountno
receivingaccountno收款账号
currency币种
paymentamount付款金额
summary摘要
bankhandletime银行处理时间
banksenttime发送银行时间
reviewtime复核时间

#### 自动核销构件日志详解

系统公共-系统设置-调度任务下的【任务管理】功能，检查【单位付款自动核销计划任务】是否启动并正常执行。查询最近一次执行的log日志。

日志路径：..\server\log下

![](images/zdhximage1.png)

![](images/zdhximage2.png)

日志关键字：

1、构件入口，进入单位付款自动核销构件：

![](images/zdhximage3.png)
获取到待核销的单据条数：

![](images/zdhximage4.png)

2、构件结束，单位付款自动核销构件执行结束：

![](images/zdhximage5.png)

**常见问题1：**1和2能搜到，但按单据编号搜索找不到，说明单据不在扫描范围内，检查单据的申请日期，是否在扫描范围（默认31天）内。

```sql
select id,requestdate from tmpaymentsettlement where docno ='单据编号'
```
3、单条单据核销入口：

![](images/zdhximage6.png)

4、单条单据查询交易明细的SQL条件：

![](images/zdhximage7.png)

5、单条单据查询交易明细条数：
![](images/zdhximage8.png)

**常见问题2：**若查询交易明细条数为0，说明没有匹配到符合条件的交易明细，关键信息不一致，根据4的筛选条件检查。


### 8.2现金银行回单进影像构件任务

#### （1）银行回单影像未自动挂载到单据上，排查步骤：

1、检查下银行交易明细的电子回单是否返回，可以从交易流水查询查看，只有回单回来了才会挂载；

2、检查下交易流水是否和单据核销，**只有核销了才会挂载到结算单及其对应的报账单上**；

3、查看调度程序的参数，如果没有配置参数的话，默认只会处理40天内的流水回单；如果需要处理范围外的回单则需要调整计划任务的参数；**修改参数后注意重启构件后才起作用**。
```sql 
//将调度任务改为非系统预置
3.1、update gspschedulerjob set ispreset = '0' where name_chs
= '现金银行回单进影像构件任务';

3.2、将任务的对象参数改为{"backDays":"50"}，50为提前的天数，修改backDays的值。
```
![descript](images/image109.png)

4、查询电子回单是否挂载imagestorageposition = 1说明已经挂完成
```sql
select imagestorageposition from bpbankreceipts where bankflowno='交易流水号'
```
5、查询挂载到哪一步了，有一条说明就到交易明细了，有两条说明就到结算单了，2条及以上看BIZTYPE，这个代表单据类型
```sql
select * from bfbillreceiptimagerecord where receiptid in
(select id from bpbankreceipts where bankflowno='交易流水号')
```
#### （2）影像上下游场景下，展示影像重复解决方式

现金银行回单进影像构件任务上增加参数："回单只挂载结算单"，key为YHZGZJSD，value为结算单据的类型枚举，例如企业付款单，其对象参数：{"YHZGZJSD":"CM_Pay_Normal"};

则只挂载和交易明细有关联关系的结算单据,不再向前挂载报账单据。

对象参数修改方式见上述修改任务的对象参数。

### 8.3余额补足计划任务

任务名称：账面余额自动补足计划任务、非直连账户余额弥补计划任务

【账面余额自动补足计划任务】弥补的是账面系统余额，包括单位端余额及资金中心余额，对应的功能 资金管理/现金管理/账户余额、资金池/柜台核算/账户余额查询。
【非直连账户余额弥补计划任务】弥补的是银行实时余额，对应的功能是 业务公共/银企服务/账户余额查询。
两个弥补任务都是取余额表最新的余额记录同步到今天，同步过程不涉及计算修改余额。

### 8.4修改任务定时器

下述以【单位上划自动入账计划任务】为例说明：
```sql
1、update gspschedulerjob set ispreset = '0' where name_chs = '单位上划自动入账计划任务'; 将调度任务改为非系统预置。
```
2、在定时器管理功能，新增定时器，选择复杂定时器，定时器表达式是cron表达式，含义参考下述，其规则是通用的，也可百度。

![descript](images/image110.png)

![descript](images/image111.png)

3、在任务管理功能，编辑构件任务，选择新增的定时器。

![descript](images/image112.png)

### 8.5银企服务相关调度任务

#### （1）付款单据一直是"等待发送银行"

1.  业务公共-银企服务-现汇业务下的【账户余额查询】功能，在线查询测试付款账户银行是否联通。

> ![descript](images/image113.png)

2.  系统公共-系统设置-调度任务下的【任务管理】功能，检查【银企-现汇-支付】是否启用且正常执行成功，并查询反馈最近一次执行的log日志。

> 日志路径：..\\server\\log下
>
> 日志关键字可搜索： PayNormal 支付
>
> ![descript](images/image114.png)
>
> ![descript](images/image115.png)

 

#### （2）付款单据一直是"银行处理中"

> 1、资金管理-现金管理-查询-【收付单据查询】功能，使用联查交易日志按钮，查看交易日志情况。
>
> ![descript](images/image45.png)

2.  系统公共-系统设置-调度任务下的【任务管理】功能，检查【银企-现汇-支付结果查询】是否启动并正常执行。查询反馈最近一次执行的log日志。

> 日志路径：..\\server\\log下
>
> 日志关键字可搜索：GetStatusNormal 支付结果查询
>
> ![descript](images/image116.png)
>
> ![descript](images/image117.png)

## 9.其他

### 9.1【通用问题】-自定义条件设置问题

现象：①扩展表单上给帮助设置过滤条件后筛选不起作用。②设置流程分支条件，流程未按预期路线流转。

原因：优先级问题，由于逻辑运算是先计算and后计算or ，or可能把and冲了。

例如在条件A and B or C中，会把A and B作为一个条件，C作为另一个条件。

①当同时满足AB，结果为真。当只满足C时，结果也为真，效果相同。

②如果是想要把A作为一个共有前提，满足AB或AC时才返回真，需要设置条件为A
and （B or C）。

### 9.2【通用问题】-没有菜单权限/看不到菜单

现象：①用户看不到菜单②在联查时提示 不具备菜单【XXX】的权限。

![descript](images/image118.png)

原因：没有给用户分配此权限（或者没有分配对），可在【菜单】功能查找此菜单对应的功能操作，然后在功能组找到相对应的功能。

![descript](images/image119.png)

![descript](images/image120.png)

### 9.3【通用问题】-IDP导出excel行数1w条限制

现象：导出excel时，数据量过大超过1W条时，会自动拆分多个文件导出。

IDP答复：

导出每个文件得最大行数是可以配置得，但是如果导出数据量过大，可能会导致内存占用过大得风险，不建议配置得最大导出行数太高。现场可以递增的方式自行调整。

修改方法：\\web\\apps\\fastdweb\\views\\extend路径下
extend.js（没有的话，手工创建js文件），增加IDP_EXPORT_PERCOUNT=具体数量。

### 9.4【通用问题】-运行时定制增加自定义筛选

以收款审核列表界面为例，收款审核界面支持运行时定制，除了产品自带的筛选外，希望增加自定义的筛选。

（1）增加扩展表单

![descript](images/image121.png)

（2）打开扩展表单的列表设计界面，拖出筛选控件：

![descript](images/image122.png)

（3）定义过滤字段和过滤方式：

![descript](images/image123.png)

（4）支持回车查询和记录最近输入内容：

![descript](images/image124.png)

（5）效果：

![descript](images/image125.png)

### 9.5【通用问题】-IDP列表自定义分页大小、分页选项

IDP实现的列表控件中，默认分页大小是50，最大分页选项是500。可支持自定义分页大小和分页选项。以收款登记为例：

![descript](images/image126.png)

效果：

![descript](images/image127.png)

### 9.6【通用问题】-IDP控件的填报说明（小问号）

![descript](images/image128.png)

效果：

![descript](images/image129.png)

## 10.问题排查方法/工具

### 10.1程序跟踪查看器

可跟踪服务器执行的SQL。

![descript](images/image130.png)

### 10.2日志级别调整（info级别）

系统默认的日志级别是error级别，日志中记录的内容有限，若开发定位问题需要详细的日志，可调整为info级别，多应用服务器部署时，需注意选择同步。

日志路径：..\\server\\log下

![descript](images/image131.png)

### 10.3流程控制台、全部流程查询

①【流程控制台】功能，只要知道单据编号，可查询任意单据的流程图。若流程为异常流程，可查看详细异常辅助分析。

②【全部流程查询】功能，若一些单据流程已完结，在流程控制台中查询不到，可以使用这个功能查询流程图。

### 10.4流程执行日志、驳回记录管理

可以辅助查询用户的操作过程日志，排查流程相关的问题。

![descript](images/image132.png)

### 10.5安全审计

对于一些用户的关键操作，系统会记录安全审计，可根据筛选条件查询，如下：

![descript](images/image133.png)