# 主动付款场景

## 1 非共享模式

GS Cloud司库系统提供了结算平台功能，统一办理各类付款请求（如企业付款、共享报账、费用报销、内转付款、上划下拨等），可支持多种结算支付方式，包括银行账户付款、承兑汇票支付（开票、背书），也可支持批量支付、拆分支付、合并支付。本章节主要介绍企业发起的各类付款业务（不含单位端发起的上划、下拨及资金中心发起的上划、下拨业务）的处理流程。

资金付款根据付款场景的不同，功能入口不同，具体如下：

| **业务场景**                                                         | **功能入口**                        | **备注**                                                                                                                                                                                                                                                                         |
|----------------------------------------------------------------------|-------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 财务部门通过资金系统发起资金付款流程                                 | 资金管理—现金管理—付款管理—企业付款 | 一般根据业务部门审批后的付款单发起资金付款流程，类似网银付款                                                                                                                                                                                                                     |
| 业务部门直接通过资金系统发起付款申请                                 | 业务公共—收付中心—业务支付申请      | 业务部门在资金系统上发起业务支付申请，申请单上只有收款方信息、无付款方信息（由财务部门经办处理），并且侧重与合同、应收应付等业务的关联，业务审批通过后，付款请求推送到现金管理下的结算平台进行结算办理，可支持银行账户支付、票据支付、合并支付、拆分支付、批量支付等多种支付方式 |
| 资金系统与前端业务系统实现接口对接，业务系统将付款请求推送到资金系统 | 资金管理—现金管理—结算平台—结算办理 | 与前端业务系统对接后，各类付款请求均对接到结算平台进行结算办理，可支持银行账户支付、票据支付、合并支付、拆分支付、批量支付等多种支付方式                                                                                                                                         |

非共享模式下，不需要进行财务共享的基础配置，不需要将单据类型加入到单据类型分组中。但业务流程任然需要在【业务公共】-【共享服务】中配置，涉及外部流程节点![](images/0fa09242f686345fea8a56583f4b5665.png)的，需要在【流程平台】-【工作流平台】中定义审批流。

### 1.1 企业付款

#### 1.1.1 业务概述

企业付款指单位发起的各类付款业务，包括单位使用自身银行账户对外支付，单位委托集团或资金中心对外支付（通过内部结算户发起付款申请）、单位发起的内部转账业务。

#### 1.1.2 业务流程

##### 1.1.2.1 流程设置

系统已预置业务流程模板，可导入模板。

**导入流程**：功能路径：业务公共—共享服务—流程定义—公共流程分配，导入后设置流程节点参与者，设置分支条件，整体流程如下：

![](images/a78106bcacdadc686f0643a854b4f06b.png)

**其中：**

**(1)内部转账条件设置如下：**

![](images/4fb82fb355ee3a6a271930499e0ba88c.png)

具体每种付款结算类型对应值见1.1.2.2付款结算类型对应值

**内部转账监听事件设置如下：**

![](images/999a09d44408c4bf8d06492f4f4e13e2.png)

![](images/dc50b7d657718ad0caa95c8262083f96.png)

**(2)委托付款条件设置如下：**

![](images/74c713cc6c8daea1d8ded360abea4795.png)

**委托付款监听事件设置如下：**

![](images/069be0aad7a093163839f97c121fb7da.png)

![](images/2656b5155b36e8832a542cc15886a4f7.png)

**(3)未满足直联确认条件设置如下：**

![](images/de7c04a2227f28894b894cd0f87ad1d1.png)

**未满足直联确认条件自处理节点执行操作设置如下：**

![](images/3edb5b65826d9d0e37f2ad270032dcf7.png)

**(4)直联付款确认条件设置如下：**

![](images/f39221526cef158dc72aff3912c0f782.png)

付款金额根据项目具体情况设置

**(5)银行付款结果监听事件设置如下：**

![](images/c666e10a30ad39c04f6e0931e1eada2f.png)

![](images/5c8d92b7dd1768a826598a44ad0472b8.png)

![](images/6dc6ae3c0a0c6ad49f1b40cbe633905b.png)

##### 1.1.2.2 付款结算类型对应值

流程设置中，根据付款结算类型判断付款路径，具体的付款结算类型对应值如下表：

| **值** | **付款结算类型** |
|--------|------------------|
| 1      | 一般付款         |
| 2      | 委托付款         |
| 3      | 拨付             |
| 4      | 零余额账户付款   |
| 5      | 内转付款         |
| 6      | 银行调拨         |
| 7      | 下拨调拨         |
| 8      | 内转调拨         |

#### 1.1.3 单据流程

##### 1.1.3.1 单位自有银行账户付款

单位自有银行账户付款指单位通过自己名下的银行账户进行对外支付。

1）出纳发起付款申请，并提交审批

功能路径：资金管理—现金管理—付款管理—企业付款

![](images/6300591a16976afb2cf5e9ee917c33d5.png)

2）审批人在任务中心中审批通过

![](images/c74e04f6da25b0707664c45027409c3f.png)

3）财务进行结算复核

功能路径：资金管理—现金管理—结算平台—结算复核

直联账户付款，复核后发送银行进行处理，如满足直联付款确认的条件（公共流程分配中设置），需进行直联付款确认后再发送银行；

非直联账户付款，付款后单据流转到结算确认环节

![](images/767162b5dd0ad30ce92bf002ebd4e031.png)

4）单位出纳进行结算确认

结算确认就是银行返回处理结果后，核销企业付款单据，通常可由系统自动确认

![](images/db1c82b4cbfa5c8abc5c87ce7b48ce53.png)

**如何启用自动结算确认？**

结算自动确认通过启用系统预置的计划任务实现

功能路径：系统公共—系统配置—调度任务—任务管理

![](images/08142dec9d441b58f2274fee578397b7.png)

##### 1.1.3.2 内转付款

内转付款是指同一资金中心下的两个单位通过内部结算账户进行结算支付。

1）出纳发起付款申请，并提交审批

内转付款时，付款方账号选择**单位内部结算户，**收款方为内部单位的内部结算户

功能路径：资金管理—现金管理—付款管理—企业付款

![](images/cb5bc8ec805b5a4f15124febd28b5aa3.png)

2）审批人在任务中心中审批通过

![](images/ca5b5b64a93b5991094b5485a5d422b6.png)

3）**单位**财务进行结算复核

功能路径：资金管理—现金管理—结算平台—结算复核

![](images/cab9ce25582d2cc92384699f1f380978.png)

4）**资金中心**进行结算办理

![](images/76c71115cb1c0d5613755bb0169396d1.png)

5）**资金中心**进行结算复核

![](images/51663da3899558e61eb13c323b404c3f.png)

复核通过后，流程结束。

### 1.2 业务支付申请

#### 1.2.1 业务概述

业务支付申请业务部门在资金系统上发起业务支付申请，申请时只需填写收款方信息，并可关联合同、应付账款等业务信息，提交审批通过后，业务支付申请流转到资金管理-现金管理下的**结算平台**，由财务部门进行结算办理，选择具体的办理方式（银行账户支付、票据支付），支付完成后向关联的合同、应付账款回传支付结果。结算办理可支持银行账户支付、票据支付、合并支付、拆分支付、批量支付等多种支付方式

#### 1.2.2 业务配置

业务支付申请需要先定义业务收付类型，然后再给每种类型定义业务流程。

业务支付申请支持根据不同的付款业务定义不同的付款类型，如合同付款、一般付款，每种业务类型可根据付款要素的不同定义不同的单据模板（可选择系统预置的合同付款、一般付款模板），可以在系统预置模板的基础上进行调整。

**业务收付类型定义**

路径：业务公共—收付中心—基础配置—业务收付类型，系统预制了业务支付申请，可根据需要使用预制类型，也可新增类型。

![](images/d5524e64ef5d9995ec22c693eb45d305.png)

#### 1.2.3 流程设置

需要设置两段流程：

一是业务支付申请单的流程，通过【业务公共】—【共享服务】的 公共流程设计、公共流程分配设置并分配流程。例如：

![](images/c40891a3517629cbf6786379a14a776e.png)

**注意**：

1）开始节点，共享任务选择“否”

2）外部流程节点![](images/fc7879acf362bf0f342bffc4ae807056.png)为审批流程，通过【流程平台】-【工作流平台】进行流程设计、流程分配，可根据实际业务需要配置具体节点

3）“结算办理”节点![](images/c968ed08feb7fb3b16bf00d558054e15.png)必须有，用于推送到结算平台进行结算办理

二是，设置结算办理后，生成的企业付款单流程。系统有预制模板，可直接导入使用。如下图

![](images/92760ee79cc0aae825d95a3dbbbacc48.png)

![](images/d79669fb6145bfab8b91b3f21e9b997f.png)

流程中各节点的具体配置同[1.1.3企业付款章节](#流程设置)的流程配置

#### 1.2.4 单据流程

1、发起业务支付申请，提交审批

![](images/9d7df45020282cba42aa163000093b0b.png)

2、单据审批

![](images/a35eaa03916479598879aca30d8712f9.png)

3、结算办理，单据流转到结算办理环节，勾选单据，进行办理

![](images/d27bf24d9d8471ae7a57306e47a443da.png)

选择付款账号，进行办理，提交

![](images/6dcb37bc7d56c82541b9edce2638a855.png)

![](images/e82c8cd2a66f6acfa575b1f803fcd1f5.png)

4、结算复核

![](images/dc73bb64efb0801be2d212e5a125392f.png)

结算复核后，单据根据配置的流程走后续节点。

如果付款账户为非直联账户，则流程结束；

如果付款账户为直联账户，根据流程中设置的条件确定是否走直联确认，走后续流程。

### 1.3 结算办理

本节主要对结算办理环节的各类办理方式进行介绍，主要包括票据背书支付、票据开票支付、批量支付、拆分支付、合并支付，银行账户支付前面的流程已包含，本节不再赘述。

票据背书/开票的结算办理流程与共享模式下相同，可参见本文档中的2.2.13共享模式 7结算办理自管票据背书/开票支付-结算办理自管票据背书开票支付和2.2.14共享模式 8结算办理统管票据支付章节-结算办理统管票据支付

在进行流程说明时，直接从结算办理环节开始，流程发起端（如业务支付申请、报账申请）不再赘述。

#### 1.3.1 批量支付

批量支付用于在进行结算办理时，对多笔付款请求，可以使用相同的结算方式进行支付的场景，如对同一个付款单位的多笔付款请求（收款人、收款账号可不同），均使用同一个付款账号进行支付。

1）结算办理时，选择批量支付

![](images/0e33cbda395ce81e7905febdbaa0d6ee.png)

![](images/393b8454d79bb8cdeaa1ab00c242a002.png)

2）审批（如流程节点中无审批节点，可省略此步骤）

![](images/bfb7771a61453dad7db76d29fa85013d.png)

3）结算复核

复核后，直联账户按照流程发送银行支付，非直联账户线下付款，付款成功后到结算确认中进行确认

![](images/55483064ae99cd13ce8a337e4b37a36b.png)

4）结算确认

![](images/a894456f028066904080c69d0edf7f5f.png)

#### 1.3.2 合并支付

合并支付是指将面向同一收款人、同一收款账户的付款请求合并为一笔付款进行处理，合并办理后，发送银行时也是一笔付款记录。

1）结算办理环节，选择合并办理，并选择结算账户

![](images/c879f9c19b809126a86d28349c0f1012.png)

![](images/d491e5bcc71ef8e521096b09939521de.png)

合并办理后生成企业付款单，付款金额为单据合计金额，保存后提交。

![](images/fed524506e28a1467821168a780ee2b2.png)

2）提交后，按照流程审批

3）结算复核

![](images/4a946cef75ddd12720af9ed3d93dba41.png)

4）结算确认

![](images/eb8dd4bf2e868f80c9cfe7db7021a035.png)

结算确认后，联查业务支付申请单据，均为已完成状态

![](images/9dccc1b6c07ebee5ae206bdd57a9004a.png)

#### 1.3.3 拆分支付

拆分支付是指将一笔付款拆成多次支付，一般用于一笔大额付款，当一个银行账户资金不足时，需要多个银行账户进行支付，或者为了合理控制票据存量，付款时首先选择金额合适的票据方式对外支付，剩余金额通过银行账户支付。

下面以一笔付款拆分为银行账户支付和票据背书支付为例进行说明;

#### 1.3.4 参数设置

拆分支付会涉及到修改原单据的结算方式，需要启用参数控制，允许办理时修改结算方式

路径：业务公共—业务配置—业务配置—全局参数配置

![](images/8cb767f34c4a092bac6a8ab7533f115a.png)

#### 1.3.5 单据流程

例如对于一笔30万的付款，办理时，首先选择票据背书支付20万，再通过银行账户支付10万

**1）票据背书支付20万**

首先，办理时，修改结算方式，选择银行承兑汇票背书支付

路径：资金管理—现金管理—结算平台—结算办理

![](images/dd09f437bb4ca004c07a23709332cea6.png)

进入票据背书申请界面，选择用于背书的票据

![](images/887ee1365193cffa6971232cfcaba96e.png)

![](images/0043c6d9e472e53d2d0bf5c0399c00f8.png)

保存后，提交，进入结算复核

路径：资金管理—现金管理—结算平台—结算复核

在自管背书类型中，找到需要复核的单据，进行复核

![](images/9e1a73dc61a4d968dd5832b707aac06b.png)

纸票背书，复核通过单据结束

直联电票发送银行，按电票流程处理

单据流程完成后，票据状态变更为已背书

![](images/e9b7a11dd570dc29701f84df161ccb9e.png)

**2）银行账户支付10万**

票据背书支付完成后，在结算办理中，可看到该笔单据已办金额为20万，如下图

![](images/defc0bd8c237975c45bd529c2583f746.png)

选中单据，点办理，通过银行账户支付剩余10万

![](images/93c54eb577560f2aef06a39d98b9f466.png)

![](images/28cf0169c38c282f1070b194737cd470.png)

保存后提交，按照流程节点，进行企业付款审批

![](images/c921ee8ddf743cfe719d29ae5150bd69.png)

审批通过后，进入结算复核

![](images/b196bcd151d62af963eb34fce1a180af.png)

结算确认后，流程结束。

查询单据状态为已完成。

![](images/607d0f9a427732214003989ed93c02aa.png)

### 1.4 企业付款单和资金计划集成

目前支持的单据范围：企业付款单、单位下拨单、单位调拨单。具体单据类型如下：

企业付款单：编号：TM_QYFKD 名称：企业付款单

单位下拨单：编号：TM_DWXBD 名称：单位下拨单

单位调拨单：编号：TM_DWDBD 名称：单位调拨单

以企业付款单为例：

**（1）配置扩展表单**

表单模板定制，放出【资金计划】控件。

![](images/87512d36c7f74770e0b4d7ab2b078e89.png)

选中【资金计划】控件，对【隐藏】属性去掉勾选。

![](images/a90d39d0f408c06cf24410e19bbca724.png)

**（2）计划控制设置**

![](images/f957e900b78211bfc418801bbce35b1d.png)

**（3）计划控制启用**

![](images/bd70eb6b0ebf00f71d27adaa4935ae5f.png)

### 1.5 企业付款单和全面预算集成

目前支持的单据范围：企业付款单、单位下拨单、单位调拨单。具体单据类型如下：

企业付款单：编号：PaymentSettlement 名称：转账付款单--主动发起的企业付款申请单和付款录入非认领模式都用这个

单位下拨单：编号：TM_DWXBD 名称：单位下拨单

单位调拨单：编号：TM_DWDBD 名称：单位调拨单

以下以企业付款单为例：

**（1）【启用预算连用】参数**

![](images/3c85bb181c52aa46aae48f301f454152.png)

**（2）业务对应定义**

![](images/267223a27a9bf09003bf22b13444fb64.png)

**（3）业务流程设置**

![](images/1164a9c26d6574a60cc5ecb46c78b193.png)

占用数/执行数控制以此处配置的为准，与共享流程节点上配置的无关。

**（4）业务控制启用**

![](images/ecf61a8f5e17848f2a60c0c6bfba9a16.png)

**（5）其他**

结算单据和预算的集成时机是通过在【业务流程设置】功能配置的方式，不支持在共享流程中设置的预算控制，如果配置了先将共享流程中配置的内容删除。

**（6）控制不住原因排查**

预算的维度和单据字段的关系，即业务对应未配置正确，尝试将下述选项设置为是后，看是否能弹出预算提示，能弹出提示说明调用到了预算接口，检查预算相关的配置。

![](images/2637d966832ce2bd499f77d53788ca67.png)

### 1.6 交易明细自动入账

#### 1.6.1 场景介绍

交易明细自动入账是指通过系统配置，实现银企直联获取的银行扣款交易明细或手工导入的银行交易明细自动生成付款单，并可根据业务需要配置生成的付款单状态。

#### 1.6.2 相关配置

1）参数“交易明细自动生成付款单据状态” 设置

功能路径：业务公共—业务配置—全局参数配置

![](images/82613af361d793cd2ff8c34330e681db.png)

2）单据默认值设置

功能路径：资金管理—资金基础—单据默认值设置

生成方式为交易明细生成，至少增加制单人的默认值配置（该制单人需要设置为行政人员）

![](images/01d6753ba709597c18dc9390fb11dd1f.png)

3）计划任务启用

功能路径：系统公共—系统配置—调度任务—任务管理

启动任务【付款结算交易明细自动入账计划任务】

![](images/49f9bbd0f8c9e55f7192a473863f5de8.png)

新的付款认领根据参数【付款录入启用发布认领】与【交易明细自动生成付款单据状态】组合出以下几种场景：

| **生单状态配置** | **付款录入启用发布认领**                            | **付款录入未启用发布认领**                                                  |
|------------------|-----------------------------------------------------|-----------------------------------------------------------------------------|
| 制单             | isClaimDoc=‘1’，ClaimStatus=5（制单）               | isClaimDoc=‘0’，ClaimStatus=5（制单，为了在付款录入能查到），并生成结算信息 |
| 完成             | isClaimDoc=‘0’                                      | isClaimDoc=‘0’，ClaimStatus=5（制单，为了在付款录入能查到）                 |
| 发布认领         | isClaimDoc=‘1’，且自动发布，ClaimStatus=1（已发布） | 不生成单据，此状态与参数冲突                                                |

isClaimDoc字段含义：是否认领单据

-   付款录入启用发布认领

（1）状态配置为制单时，自动生成制单状态展示到付款录入界面；

（2）状态配置为完成时，自动生单到完成；

（3）状态配置为发布认领，自动发布（自动发布认领目前存在无法精准识别的问题，比如：交易流水号改变了的情况。后面考虑实现精准识别后，再支持）

-   付款录入未启用发布认领

（1）状态配置为制单时，自动生成制单状态展示到付款录入界面，并生成结算信息；

（2）状态配置为完成时，自动生单到完成；

（3）状态配置为发布认领，不生成单据，此状态与参数冲突

### 1.7 移动审批界面配置

单据适用范围：单位调拨、单位上划、单位下拨、企业付款

场景：默认展示的是基础表单的移动格式，和基础表单相同的话，无需配置。若项目需要显示一些自定义字段或者展示审批日志信息，可以配置扩展表单的移动表单，以单位调拨单为例：

功能路径：业务公共—业务配置—表单配置—表单模板定制

**（1）表单模板定制-扩展表单-移动设计**

![](images/d0118ea9f88d4a2b6a694b15f4153551.png)

**（2）展示自定义字段/展示审批日志**

![](images/33451f96092e4c32e385df004d1679ba.png)

![](images/f99a622e83f561c29ec7c6877bd5c20b.png)

单据类型：单位调拨：TM_DWXBD、单位上划：TM_DWSHD、单位下拨：TM_DWXBD、企业付款：TM_QYFKD

**（3）效果**

![](images/8beaa5ed3f1c60fbd4367c83383f7074.png)

## 2 共享模式

共享模式，即资金结算业务纳入财务共享中心处理，按照共享的流程、任务处理模式来管理。

### 2.1 公共基础配置

#### 2.1.1 财务共享模块相关配置

资金结算业务进共享，需要配置财务共享的基础初始功能以及启用一些参数，主要包括以下相关内容：

##### 2.1.1.1 共享服务定义

设置财务共享中心，将单位加入共享中心并启用。

功能路径：财务共享—运营支撑平台—服务定义—共享服务定义

![](images/f8b14559717d690823d74d16e4a34da1.png)

##### 2.1.1.2 业务组定义

设置共享中心中的业务组，资金结算业务可定义为资金结算组。

功能路径：财务共享—运营支撑平台—服务定义—业务组定义

![](images/7bef865173f2122d77497b51d055f19c.png)

##### 2.1.1.3 作业人员定义

将结算相关人员加入到业务组中。

功能路径：财务共享—运营支撑平台—服务定义—作业人员定义

![](images/6d7d1beb086d224c2b8bf5896133b6cb.png)

##### 2.1.1.4 单据类型分组

单据类型分组是对各类报账单的分类。可将资金相关的单据放在一个分组中，可根据需要灵活设置，并将加入的单据应用到对应共享中心中。

添加到单据类型分组中的单据，才能在我要报账菜单中选择到。

功能路径：财务共享—运营支撑平台—服务定义—单据类型分组

![](images/bd955b5f49720039af3e8367d8a72746.png)

##### 2.1.1.5 单据类型设置

对各类报账单设置相关凭证生成、电子影像等相关参数。

功能路径：财务共享—运营支撑平台—服务定义—单据类型设置

![](images/7dbc7ba5ec8cd8ee8d4f946501640bac.png)

#### 2.1.2 资金相关配置

##### 2.1.2.1 启用结算办理参数

资金结算都通过现金管理下的结算平台来处理，需要启用结算办理参数。

功能路径：业务公共—业务配置—业务配置—全局参数配置—业务公共—收付中心

![](images/aa4166f8b3ebc258c23f97137a2cc1a4.png)

##### 2.1.2.2 资金机构核算进共享

如果资金中心相关的业务，如上划下拨、内部计息等业务生成共享中心财务凭证时，则需启用该参数。

功能路径：业务公共—业务配置—业务配置—全局参数配置—资金池

![](images/846df48ccae6721675979533655f7d12.png)

##### 2.1.2.3 结算办理是否允许修改结算账户、结算方式

在结算办理环节，如果单据已有账户信息，此参数用来控制是否允许修改结算账户、结算方式。

功能路径：业务公共—业务配置—业务配置—全局参数配置—资金管理—现金管理

![](images/52122ffe4ee719611a7223b51c225afd.png)

##### 2.1.2.4 启用跨币种付款

如有跨币种付款业务，可启用该参数，启用后，结算办理时可选择到外币账户。

功能路径：业务公共—业务配置—业务配置—全局参数配置—资金管理—资金基础

![](images/7dabc2c27f2d7e82f7afeac1147ee657.png)

### 2.2 付款业务

#### 2.2.1 业务概述

付款业务即各类付款申请，如一般付款申请单、合同付款申请单、保证金付款申请单等各类对公付报账单。收付中心预制了五类基础模板，通过【业务收付类型】集成各种业务场景，支持多个发起口做单，支持资金与非资金模式，也支持从业务支付申请发起或者共享我要报账发起。 目前集成了如下业务：

![](images/f20aa8a0e22e96d92d861b1d2a219d03.png)

【业务收付类型】集成了场景定义，以及与预算、影像模块的业务集成。

![](images/6e56ce9085db3a80c63da9c4981ec360.png)

编号：项目自定义即可

名称：项目自定义即可

对应表单模板：产品预制的五类基础表单，如图：

![](images/b3ed4205ad4abd0d06789fae53590226.png)

业务类型：

一般付款单适用于企业对外经营付款、融资业务的主动还款、主动发起的票据兑付等场景；银行扣款单适用于被动扣款业务，如付款认领认领的扣款业务、融资模块的被动还款业务、资金中心端业务发生后对成员单位的变动通知；商旅付款单适用于对账单生成的商旅结算业务；退款重付适用于银行交易失败退款后通过收款认领做的重付业务。

境内跨币种：跨币种场景需要开启，全局参数【境内启用跨币种付款】开启后才显示。

境外付款：境外跨币种场景需要开启，全局参数【启用境外资金业务】开启后才显示。

表单样式：

![](images/abb1ac1faadee395049719a1d330b659.png)

如果设置为‘收款账户在主表选择’，则业务支付申请在保存时会校验主表的收款账户和支付明细子表的收款账户保持一致，以防止出现付款错误。

报账开票：若想要在报账环节开票，需要勾选该复选框，否则无法看到开票按钮。（制单环节不显示，在财务审核初审环节显示该按钮）

订单付款：订单付款如有即挂即付场景应付结算单必须同时启用订单付款。

支付明细不自动生成：复选框勾选后，业务明细不自动生成支付明细。

定义好收付类型后保存，保存后手工点击下图中的对应按钮可以将对应的收付类型同步至预算、影像等模块以实现对应收付类型的控制。

![](images/79a724dcef08ebfc81cf227032ca3048.png)

预算业务类型：如果项目开启了预算控制，建议同步，如果不同步以收付中心预制在预算模块的基础业务类型控制

![](images/794fb6a0be663617c3c3f62762621db3.png)计划业务类型：如果想受资金计划控制，需要同步计划业务类型。建议同步，如果不同步以收付中心预制在预算模块的基础业务类型控制

![](images/794fb6a0be663617c3c3f62762621db3.png)

影像约束类型：需要同步，以实现表单与影像发票之前的控制

![](images/b5f70506cb4842650b7e8a65a9779054.png)

业务类型定义：同步至业务事项关联单据类型，如下图，可实现根据单据类型过滤业务事项。

![](images/1795101af22b20ef5f62901ffb539524.png)

#### 2.2.2 业务流程举例-业务支付单

下面以业务支付单为例进行说明，该单据即一般对公付款单据。

##### 2.2.2.1 定义付款报账单

功能路径：业务公共—收付中心—基础配置—业务收付类型

通过【业务收付类型】定义功能新增报账单类型-业务支付单，对应表单模板为一般付款单，关联的业务类型为经营性付款（其他类型单据，根据实际业务需要选择）如下图：

![](images/38ccc74e28a951e2ff03ecf3e5ad5c74.png)

##### 2.2.2.2 定义报账单格式

以一般付款单模板为基础，扩展新的表单-业务支付单（如系统预置模板满足业务需要，可直接使用，不需再单独扩展）

功能路径：业务公共—业务配置—业务配置—表单模板定制

具体配置方式同[2.3.1 定义报账单格式](#_基础配置) 章节

##### 2.2.2.3 定义业务流程

功能路径：业务公共—业务配置—共享服务—公共流程设计/公共流程分配，先进行公共流程设计，再进行公共流程分配。

对于一般付款类报账业务流程，需要配置两个流程：

一是报账单的业务流程，以进结算为例，例如如下流程 ：

![](images/dc5f7b2fe401929feb099cd0729ae1cd.png)

注意：

1.  需要根据办理权限情况，配置各节点的参与人，可在公共环节添加参与者
2.  流程分配时，选中开始节点，共享任务参数选“是”

![](images/9ddd6ed90a48683c77bdd7b425972849.png)

3）流程设计、流程分配保存后，要“启用”

二是付款结算单流程，即结算办理后，要走的流程（结算办理后会根据付款报账单的支付明细生成付款结算单），例如如下流程：

![](images/7ba63ee0710cee2fef7da676c7d36250.png)

鼠标选择空白处，将右侧共享模式改为否

![](images/278d08189f9976290c1ea73ae66e1c9b.png)

注意：由于付款账户有直联和非直联之分，所以，结算复核后要加分支控制；对于直联账户付款，又根据审批需要，有是否走直联付款确认的区别，需要配置分支条件，以上流程各节点在**公共流程分配**环节配置示例如下:

![](images/9c9ed89c2a3666c903e9e0cb4cdaec4b.png)

![](images/0d06cb82b07e6b731e90260dd25b9889.png)

注：每种付款结算类型的值详见**2.2.2.7 付款结算类型对应值**章节

![](images/eb2addf556b9960f74e1809d7920690f.png)

![](images/e4396788afd105d9a886ad068db66a20.png)

![](images/7521973fe4c4386c6d997f3a95d0cf79.png)

![](images/30be8b94513c34d9332beec2edc20238.png)

##### 2.2.2.4 加入单据类型分组

功能路径：财务共享—运营支撑平台—服务定义—单据类型分组

![](images/772822b972fb5855cf3f0b485ad3647a.png)

##### 2.2.2.5 单据类型设置

功能路径：财务共享—运营支撑平台—服务定义—单据类型设置

主要设置凭证生成方式

![](images/e79026e3c75b464ea937761181eb9845.png)

##### 2.2.2.6 业务流程

**1）发起付款申请**

功能路径：财务共享—网上报账服务—报账服务—我要报账

制单，保存后提交

![](images/4ed90a9a76ff05d339af1fe1d72eccff.png)

如果此处找不到单据，请检查【单据类型分组】中是否已添加

![](images/fb5f5b4bce1a8e2a318d14eeb90df901.png)

**2）按业务流程进行处理**

按流程进行业务初审、财务审核，然后到凭证生成环节

![](images/3cc918244dc35a45d2ee8fa57a155a06.png)

**3）凭证生成**

凭证生成需要到功能菜单中进行处理

功能路径：财务共享—财务作业中心—报账处理—凭证生成

生成凭证，保存后，返回列表界面，点通过

![](images/0ea2e811757bf7909c61ba16e18c54e9.png)

![](images/e7e193f3b73794a68166ada0b31ec47a.png)

![](images/1561fe0d4f870d631b97883e3d7486d1.png)

**4）结算办理**

功能路径：资金管理—现金管理—结算平台—结算办理

单据流转到结算办理环节，需到功能菜单中办理

![](images/423b8c55e9ee6d99c0ab51dce059e85f.png)

![](images/09bc37a31a6820e86f3a67ea1eae92d8.png)

选择付款账户，保存后提交

![](images/1eef8b1b10311c56373009e9023a6cbe.png)

**5）结算复核**

结算办理完毕，生成企业付款单，到结算复核的企业付款单中进行复核

功能路径：资金管理—现金管理—结算平台—结算复核

![](images/3c5dc5d95d8ed4e3f888906e5af4f1cf.png)

**6）直联付款确认**

根据配置的流程条件，需进行直联付款确认，确认后发送银行处理。

功能路径：资金管理—现金管理—结算平台—直联付款确认

![](images/5d6903f4e648bfa951f1322a81274c62.png)

**7）结算确认**

结算确认即付款核销，根据银行返回的交易结果进行核销处理

功能路径：资金管理—现金管理—结算平台—结算确认

![](images/809d37ae9490c3213096dba66ab06a65.png)

核销：勾选付款单据和银行回单，进行核销

直接确认：对于无回单、非直联或无法返回回单但已确认付款成功的单据，可直接确认。

结算确认后，流程结束

##### 2.2.2.7 付款结算类型对应值

| **值** | **付款结算类型** |
|--------|------------------|
| 1      | 一般付款         |
| 2      | 委托付款         |
| 3      | 拨付             |
| 4      | 零余额账户付款   |
| 5      | 内转付款         |
| 6      | 银行调拨         |
| 7      | 下拨调拨         |
| 8      | 内转调拨         |

#### 2.2.3 业务流程举例-待付款清单推业务支付申请

产品支持从待付款清单直接推送到业务支付申请付款，可以方便用户操作。只支持共享模式。

##### 2.2.3.1 定义流转规则

待付款清单列表，增加全局扩展，增加扩展后需立即保存（切记），然后方可增加流转配置。

功能路径：业务公共—业务配置—表单配置—表单模板定义

![](images/71238c9567d53b1380839a5c7ffed46e.png)

点击表单设计打开表单设计界面然后点击流转配置

![](images/3ac2b2368a24080a85c95885e1412170.png)

打开流转配置界面，产品预制了生成一般付款单和合同付款单的两个流转规则，项目可以按需使用。选择对应的某一个点击复制，建立自己的流转规则。

![](images/c3b471a9e8917a2f842c7dee7f530f41.png)

##### 2.2.3.2 业务流程

打开待付款清单功能，选中要生成业务支付申请的应付明细，点击生成业务支付申请

功能路径：业务公共—收付中心—付款业务—待付款清单

![](images/62c11073185a1d06a761c97e9ccb3a25.png)选择对应的流转规则

![](images/830417f21e2547a92b71a7753d776cbe.png)

打开制单界面，根据配置的流转规则带数据到业务支付申请。

![](images/2a18b63727c3bcded187211da91b29a6.png)

后续流程同2.2.2.6章节，不再赘述。

#### 2.2.4 业务流程举例-销售退款业务

用于我方销售商品并收款后，由于各种原因(比如因质量原因对方退回部分商品)，我方需要给买方退还部分款项的场景。可关联销售合同，核销应收红单或预收单

功能路径：业务公共—收付中心—付款业务—业务支付申请

![](images/2ec4f5e1f0c8da97bdda854ead9b00f5.png)

#### 2.2.5 业务流程举例-合同付款业务

功能路径：业务公共—收付中心—付款业务—业务支付申请

单合同付款：每笔付款只能关联一个合同，付款完成，可回写合同收付款记录

![](images/091cbc5e225b2655480f20f07f080fbc.png)

![](images/4b136c82ffca66bece708efe72d5139c.png)

多合同付款：每笔付款可关联多笔合同，付款完成，可回写合同收付款记录

![](images/ecd261390955bed0fd85ebc64da43bb2.png)

联合体合同付款：联合体合同包含多个合同往来单位，付款时，如果关联的是联合体合同，则可同时向多个往来单位付款

![](images/f0f0b4b6bbfb5f24cf016e44ff54231f.png)

注意：款项性质需要勾选影响合同金额，回写的合同已收付金额。

#### 2.2.6 业务流程举例-商旅付款业务

商旅结算单用于共享下的差旅服务平台，进行对账后的差旅结算

功能路径：财务共享—差旅服务平台—对账结算—对账单生成

![](images/1.png)

![](images/75907012154bdbe67b6dbc25b8dae483.png)

#### 2.2.7 业务流程举例-预付付款

用于支付预付款项，可单独定义一类单据，支持待收到发票挂账时与预付核销。

开启如下参数：功能路径：业务公共—业务配置—业务配置—全局参数配置：【启用与应收应付集成】

![](images/e14503ca579078a48d59e792ced8bbb0.png)

功能路径：业务公共—收付中心—付款业务—业务支付申请

![](images/28e50b5544f652fd5f50f23a9bfe56d4.png)

流程不再赘述，走完结算流程后产生预付单，再去应付里面核销。

![](images/bb2bb91cc23bd3131c776d00a46bc970.png)

#### 2.2.8 业务流程举例-应付付款业务

用于应付账款的支付。可与应付账款进行核销，可参照应付账款生成付款单（需配置流转规则）

开启如下参数：功能路径：业务公共—业务配置-业务配置-全局参数配置：【启用与应收应付集成】

![](images/e14503ca579078a48d59e792ced8bbb0.png)

![](images/f42c6e64bd7a858118347879008b2025.png)

也可使用参照模式

![](images/2.png)

两种模式不可同时使用，只能选其一，项目上线前最好确定好模式。

#### 2.2.9 业务流程举例-与预算集成

业务支付申请支持根据业务收付类型，通过在共享流程中配置，占用预算

1）在【业务收付类型】功能中，选择对应的收付类型生成预算类型

![](images/a5fd2159c3100fa526836d5a257bfae5.png)

2）在预算的【业务对应定义】功能中，引入新增的预算类型(单据类型)

![](images/e713b5eb74a2b0af12510b6ac7cc5723.png)

3）在【公共流程分配】中，选择对应的流程配置预算的控制方式

![](images/53c1f28c2baf4dce2b23107c24f8374b.png)

#### 2.2.10 业务流程举例-银行借款主动还款

##### 2.2.10.1 增加付款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表单模板选择“一般付款”，业务类型选择“银行借款还款”

![](images/0d4156520d450852dfb7df7f3a35b442.png)

##### 2.2.10.2 定义业务流程

同2.2.2.3章节配置

##### 2.2.10.3 定义单据格式

功能路径：业务公共—业务配置-表单模板定制

定义对应收付类型的扩展表单

![](images/b2bafbac46b8c76667092ef5042a2f4b.png)

将此类型对应的表单的借据相关字段放出来

![](images/884670de321e98d49fd1f2e0120e7ce2.png)

#### 2.2.11 业务流程举例-境内外跨币种付款

##### 2.2.11.1 参数配置

业务支付申请支持跨币种付款，首先开启如下参数

![](images/99257f412bc765ad93a3412c926068e4.png)

对应收付类型开启

![](images/293ac8549919c6912dd6d074bb082fb1.png)

##### 2.2.11.2 定义业务流程

同3.23章节配置

##### 2.2.11.3 表单设计

功能路径：业务公共—业务配置—表单配置—表单模板定制

定义跨币种付款表单

![](images/b90ab3e6d09ab7db879ff69ee0dd3eb0.png)

表单样式如下，需要将交易金额、交易币种、交易汇率放出来

![](images/df77399bc1ecf2650afd47fa5fe67dfa.png)

#### 2.2.12 业务流程举例-非资金结算场景

##### 2.2.12.1 场景介绍

业务支付付款单单据不流转共享结算平台，不流转资金结算，客户的实际结算目前不在系统中或者是一期不在系统中具体结算，仅新增单据核销应收应付或者与合同对接，需要审批流

##### 2.2.12.2 相关配置

通过公共流程设计、公共设计并分配单据流程

功能路径：业务公共--共享服务--流程定义—公共流程设计/公共流程分配

流程示例：

![](images/808b85751a564c5e5388223037c3b52a.png)

选中空白处，右侧设置共享任务为是，如下

![](images/b28ea01f7a70c91781b6eb2766ee791c.png)

外部流程节点：

![](images/b7e620f59ce09441287fc960a75889d1.png)

自处理节点：

![](images/994c406f77da2d8adb0c06e45771d07a.png)

#### 2.2.13 结算办理自管票据背书/开票支付

##### 2.2.13.1 业务概述

自管票据支付是指在结算办理环节，通过单位自有票据背书支付，或者开出新票进行支付。

自管票据背书、自管票据开票支付在流程上类似，此处放在一起进行说明。

##### 2.2.13.2 业务配置

##### 流程配置

自管背书/开票流程较为简单，结算办理生成对应的单据后提交发起流程，对于纸票进行结算复核即可。对于电票，复核后需要等待电票支付结果，待对方收票成功后推动单据流程至完成。如开票失败或者对方拒签，会将单据退回至结算复核环节。

![](images/021d50eba5ce3b16e491fe7eb0c819bd.png)

分支控制节点设置如下：

![](images/77d24474c9ed2f9f1d5bb944a3d0578b.png)

分支条件设置如下：

是否直联isebc=1，即直联电票需要走监听，非直联电票无需走监听

![](images/867b1f209b331e1d96fa32587f3b1dbd.png)

![](images/32029ea76ecdeac0eb6568fbbd7b9182.png)

监听事件节点设置如下：

![](images/9879e02216391f3098cbcf59644ae4dc.png)

![](images/6e9292419366f488b416ddcafa0cfe1b.png)

##### 2.2.13.3 单据流程

###### ①结算办理-票据背书

结算办理环节，结算方式选择票据，票据类型选择自管票据背书

![](images/a823f7f4f445cb011512928abacea143.png)

办理方式选票据相关信息

![](images/9986d444fa13f929556ea0e81af17902.png)

确认后，进入票据背书界面，选择背书票据后提交，如下图：

![](images/368a0528a70f3a26d2a8c937e366aaad.png)

![](images/c5fb4f879f3d500304045dba1d74f061.png)

进入结算复核如下

![](images/98c2516f8fa7aebe8e41bdc8b99b9bfc.png)

结算复核后，如果是电票，后续走电票流程。如果是纸票，流程结束，票据状态变更为已背书。

![](images/de5bffd0868a6017e42bcb935298f385.png)

###### ②结算办理-票据开票

结算办理，选择票据开票

![](images/c172c8590f65b1410960170171feafae.png)

确认后进入开票申请单，添加开票明细后提交，如下图

![](images/61df840842e58051c12c095f795a6625.png)

待办事项中处理

![](images/830c6d5700d1e83d513d060afb103b59.png)

结算复核，对于纸票，补充票据号、开票日期、到期日信息，通过后流程结束。

对于电票，需发送银行，到票据管理模块中进行办理，待对方收票成功后推动单据流程至完成。

![](images/f4f609b3d48796befc50e4a85223861a.png)

#### 2.2.14 结算办理统管票据支付

##### 2.2.14.1  单据流程—统管背书

报账单前面流程相同，不再重复。此处从结算办理环节开始。

单位结算办理统管背书，票据类型选择集中用票，发起背书申请，提交审批

![](images/ee9a3d875bf39e848bde4c257ad2f88a.png)

审批后，资金中心进行结算办理，如下图

![](images/0d2775bd060e9177fa86c7bb8c614c02.png)

选择背书票据，提交，根据票据形式的不同，走后续流程。

纸票经结算复核后，流程结束；

电票发送银行后，待返回监听结果后结束。

![](images/1cc9b42521cd15166f510abd0fb5c79c.png)

##### 2.2.14.2 单据流程—统管开票

单据前面的流程不再重复，从结算办理开始

成员单位进入结算办理，选择办理方式，如下图

![](images/c2bb96a955c2c4d256979c3c11bfd2a7.png)

确认后，进入统管开票申请界面，选择票据类型、内部保证金户，发起开票申请。

![](images/2908733e817bf0a310fbe888dff33d94.png)

审批后，资金中心人员进入结算办理，进行统管开票办理，如下图

![](images/d7c620b8d9fc151010fe3ab371a069cd.png)

![](images/ef3975144f2292153b5a4fb578176ae0.png)

资金中心人员进行结算复核，

如果是纸票，填写票据编号，通过后流程结束

如果是电票，发送银行，后续在资金池—票据池模块中处理。

![](images/4711127a6c8b5e7d881deae9f19a6c29.png)

![](images/b6234878154df2249b9ef0da11a50433.png)

开票后，生成结算凭证

![](images/19299e19eaf0eff32ce2dbf73ad52969.png)

**注意：**

复核通过时报错：“获取内部账户实体失败”

![](images/70a9d641f07f08ce1c980c9eab6bd83d.png)

处理方式：需设置**资金中心应付票据户**

![](images/032ba0e93b0efd46529a9bdb4a889977.png)

#### 2.2.15 收款方退款、银行交易失败退款流程

##### 2.2.15.1 业务概述

对公付款的退款流程主要针对银行交易失败退款或者收款方退款业务，目前系统主要提供了两种方式进行处理。对于银行交易失败退款的业务，可以进行红冲或者重新付款；对于收款方退款的业务系统可以进行红冲。其中，重新付款流程，重新生成一笔单据进行付款；红冲单据流程，生成一笔红冲单，单据相关的计划、预算、合同、应付会进行红冲处理。

目前收款方退款仅支持业务支付申请做的付款业务红冲，银行交易失败退款支持来源于报销单和业务支付申请的退款红冲，不支持来源于应付的。对于不支持场景产品正在陆续支持中。

##### 2.2.15.2 业务配置

①流程配置

（1）流程设计（流程仅供参考，项目上根据实际需要进行配置）

![](images/b2a0d8d3434e2312ed7d43da18ef006c.png)

（2）流程分配。付款流程分支作为兜底分支，红冲分支设置条件为：表单字段REFUNDWAY 退款处理方式的值为1。

![](images/943fe75da822fbed562db89cefe53b3f.png)

![](images/1258adb2be939b642cec6a74cb1d6a42.png)

注：红冲单流程走完之后释放之前的占用的合同、计划等业务数据。

②参数设置

对于来源是企业付款单的银行交易失败退款重新付款业务，需开启参数“支持企业付款银行交易失败退款”

功能路径：业务公共—业务配置—业务配置—全局参数配置

![](images/0a987ea0b2dab4db1f4b23b31d40a996.png)

认领界面如下

![](images/df5b44c2e66018169b9153bc88140978.png)

选择已退款明细

![](images/f1f8a9540ec0bdc010dfee0be81d4768.png)

点击确认，生成新的付款单（可修改收款账户、付款账户、转账附言等）

![](images/bbcf5eb55d528e11b36edd75d6430625.png)

##### 2.2.15.3 业务流程

对于银行退回的交易流水，进行流水登记发布，然后在收款认领处点击认领，选择收款方退款或银行交易失败退款：

![](images/33bfcd04b2cf8da645e4630a312916c9.png)

点击确认打开对应付款单的红单界面。

![](images/42d53fd203f884a9a2d8bfd8abdde7e7.png)

保存提交单据，财务审核后完成资金记账，如下

![](images/3a6aec5b5657d1320eced260adae814d.png)

#### 2.2.16 收款方退票

##### 2.2.16.1 业务概述

对公付款的退票流程主要针对在通过开票或背书付款时，收款方拒收票据，需要对付款单进行红冲处理的场景。红冲流程会生成一笔红冲单，单据相关的计划、预算、合同、应付会进行红冲处理。

##### 2.2.16.2 业务流程

![](images/8600fdeeda2612928c7656eedb7748ca.png)

收款认领时，认领类型选择“收款方退票”，处理方式选择“红冲”

![](images/79ef9706b8520922264ad9e5a7e84235.png)

如果存在多条业务明细时，需要手工进行红冲数据分摊

![](images/74ef98ea3e67a17f853cede0b8eb5e2b.png)

#### 2.2.17 移动审批配置

如果项目使用共享的审批节点，则需要在表单模板定制分配第二维度为审批格式的表单，如下图所示

![](images/c9105c43db2a29aa2cfaa82aaa9522d0.png)

**移动端子表模板设置**

子表字段配置好后，选中扩展子表1，点击list全局模板；（如不配置，则在移动端不显示数据）

![](images/6364bba01e5b3b51a1cb20e77161680f.png)

在list全局模板中，首先点击选择模板，选择标准列表，点击确定。

![](images/1c0283f8a3742660af11a105e2b298e2.png)

在模板中，填写对应的扩展子表1的字段
