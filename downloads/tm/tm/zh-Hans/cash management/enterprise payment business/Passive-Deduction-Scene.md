# 被动扣款场景

## 1 业务概述

被动扣款业务中，付款认领主要面向银行扣款业务生成的付款单据进行认领，如银行扣划的手续费、银行借款的还款、票据兑付等业务。

## 2 业务流程举例—银行借款还款

### 2.1 增加付款业务类型

功能路径：业务公共—收付中心—基础配置—业务收付类型

注意：对应表达模板选择“银行扣款”，业务类型选择“银行借款还款”

![](images/706d884f21379232c2952440f6ea2737.png)

### 2.2 定义单据格式

功能路径：业务公共—业务配置—业务配置—表单模板定制

![](images/21310862ef36151bfc0dd8cdc9418abd.png)

保存后，左侧会显示出刚增加的“银行借款还款”表单，选中表单，点“表单设计”，打开表单设计界面。系统中有预制模板，可直接使用，可以根据业务需要修改。表单格式定义完成，保存即可。

![](images/c787e2e13b7018764f350bb388c795fb.png)

### 2.3 资金认领类型定义

功能路径：业务公共—收付中心—基础配置—资金认领类型定义

![](images/867d9cdeb44cc1ab3de8b6c6f6a64bda.png)

定义付款认领业务类型，并关联前面定义的银行借款还款单。

### 2.4 定义业务流程

通过业务公共—共享服务—流程定义—公共流程定义/公共流程分配进行流程设计，并进行流程分配

![](images/0573aa960d8c36217553dfad46849a01.png)

如：定一个简单流程，并分配完毕

![](images/d9312ceac60f7d71bb4d56abfe56555b.png)

### 2.5 付款录入，并发布认领

功能路径：资金管理—现金管理—付款管理—付款录入

银企直联账户，可通过勾选银行流水，点登记按钮，登记银行扣款单，并发布认领；

非银企直联账户，可手工登记银行扣款单，并发布认领。

**注意：**在付款录入界面，可登记银行流水只显示业务参考号为空的交易流水，同时过滤掉上划下拨的明细，即通过系统直联支付的不会在此显示，直联账户转线下付款的也会在此显示。

![](images/4db605a2b7ac0b78116390b39d690f09.png)

### 2.6 付款认领

功能路径：业务公共—收付中心—付款业务—付款认领

付款认领，选择认领类型

![](images/ca8256f0d6eb6462976126098f793071.png)

确认后打开付款认领报账单，即银行借款还款报账单，如下图，填写业务明细，选择银行借据，录入还本、还息金额，保存后提交

![](images/0a678b9c0a1c316ed619b5a0e705cee8.png)

可通过流程中心进行审批

![](images/c0cb3bdea08d67dfba33843d4ed36172.png)

认领完成，可在付款认领中查询。

![](images/7126b9d5b4a7f427fee12d73c23c26e4.png)

## 3 付款回单自动发布认领

同收款自动发布认领一样，付款银行流水（主要是银行扣款、线下支付、导入的非直联流水）也可实现自动发布认领，具体配置如下：

### **1）参数配置

功能路径：业务公共—业务配置—业务配置—全局参数配置

![](images/e82838e0b32c19470acdfc82a99a2140.png)

### **2）单据默认值设置

功能路径：资金管理—资金基础—系统设置—单据默认值设置

![](images/4de75427e2790daee94bfadcb1df1c0f.png)

### **3）启用构件

功能路径：系统公共—系统配置—调度任务—任务管理

![](images/6d79994386ad120955266171946f5058.png)
