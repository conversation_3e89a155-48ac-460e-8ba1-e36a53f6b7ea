# 账户开户

## 1 银行账户开户

### 1.1 基础配置

#### 1.1.1 表单格式

路径：业务公共—业务配置—业务配置—表单模板定制

打开功能后，选择资金管理-账户管理下的银行账户开户表单，在右侧选择增加扩展类型，支持全局扩展和类型扩展，下面以类型扩展为例：选择扩展类型与维度，填写表单名称。

![](images/d46d162d5577545bc5aee0d612353ccb.png)

保存后，左侧会显示扩展的“银行账户开户”表单。

![](images/a65da0357af26a1137c5a36de2efb14a.png)

选中表单，点“表单设计”，打开表单设计界面。

![](images/78004d4a777028e921cf3b7da860bdfc.png)

点击默认值，是否任务默认值为1

![](images/7189cdd5b65749e62a63a52a6809d89f.png)

说明：是否任务是1，走业务流程，根据流程定义章节进行业务流程定义（审批流程中的启动方式为**外部流程**）；

是否任务是0，则只走平台审批流程，审批流程中的启动方式为**直接启动。**

其他字段可以根据具体项目业务需求修改。

#### 1.1.2 流转配置

点击表单设计中的流转配置，如下图

![](images/b1f054917ba46dc607a1e5bdb7ba8986.png)

进入界面，点击复制按钮：

![](images/1a3112e29b288e44c30ed4450dbaf37a.png)

弹出界面，

![](images/d7ed5c8c8388a075f723f28d7f875dc1.png)

**说明：**开户申请、办理生成账户，通过这个设置可以将开户界面的自定义字段传递到账户信息上；如果有扩展字段，目标表单需要选择扩展表单。

确定之后

![](images/e5c32996424b70261451dcf066d4cade.png)

点击配置按钮

![](images/ec2dc3d1543d0287a60f13ebd1cecf84.png)

![](images/a7f4b9609deba18643650fb566bc4998.png)

切换到映射关系页签，根据需要进行设置：

![](images/f0bb72ac1a26624d0fa0813802b44827.png)

#### 1.1.3 业务流配置

路径：业务公共—共享服务—流程定义—公共流程设计/流程分配

银行账户开户流程在流程设计功能中有预制流程模板，可直接导入，若银行开户岗位与启用岗位分离，需要手动启用的，则选择银行账户开户流程模板，若无需手动启用；则可以选择银行账户开户（自动启用）流程模板。

![](images/fb492363238dfacf5e4447790cd451bc.png)

模板导入后，打开导入的流程

![C:\\Users\\<USER>\~1\\AppData\\Local\\Temp\\QQ_1732762266735.png](images/17ee073db41593496c3074005d6f4af3.png)点击编辑按钮，根据客户实际情况，设置办理、启用环节的参与者（可以办理此业务的岗位/用户）。设置完成后，需要保存并启用。

**注意：业务流程请务必不要自行修改，若有修改需求，务必与产品部研发沟通确认！！！**

![](images/8030a5fed8f385672777b4318b493f76.png)

随后在流程分配功能中，新增账户开户流程。

![](images/ef54aafdcd232ba827c3532c10b7f243.png)

选择流程设计环节新增的银行账户开户流程

![](images/74eb146b07ac2591fe9c7203bbd45291.png)

确定新增后，在左侧找到新增的流程，打开后启用即可。

![](images/de3ae9cc4870d2a50e445e54cfddf7e2.png)

![](images/38703ce44a314313a82c7440b1061f1c.png)

#### 1.1.4 审批流配置（外部流程节点）

路径：流程平台—工作流平台—流程建模—流程设计

打开流程设计功能，在左侧资金管理-账户管理目录下找到银行账户开户，新增审批流，流程分类选择银行账户开户(PF)，启动方式选择外部流程。

![](images/c7563cb660648b8b62b26dd90f1716f5.png)

根据客户需要，设置审批流程，设置完成后保存、发布。

老的流程分类（开户申请单），标准产品中考虑既有项目可能存在流程中的单据，没有删除，新项目可以在流程分类功能中将其删除。

![](images/abcfcaad727eab118dd9fe4ca249b8fe.png)

### 1.2 业务流程

开户申请的主要流程为申请-审批-办理-启用-初始

1.  单位出纳提交开户申请。
2.  单位财务主管审批通过后，出纳到银行办理开户。
3.  返回的开户信息由单位出纳维护到系统中，生成账户信息。
4.  启用账户。
5.  账户余额初始。

#### 1.2.1 账户开户

##### 1.2.1.1 开户申请

路径：资金管理-账户管理-银行账户-开户申请

打开开户申请功能，点击新增按钮，新增一条银行账户开户申请

![](images/6f9e6c1b69e78e7504dcad083fc9ccb7.png)

部分字段用途解释：

1.  【申请单位】：行政组织，指使用账户的单位。如开户单位具有法人属性，则默认带到法人单位；如开户单位是核算组织，则默认带到核算单位。
2.  【法人单位】：具有法人属性的组织机构，指有到银行开户的法人资质的单位。
3.  【归集户】：选择该账户是否被归集。
4.  所需资料信息目前仅供备忘勾选，对附件等无控制。
5.  透支额度与控制方式需要在【开户办理】功能中，生成账户后方可填写。

填写完成后点击保存，进行开户审批

![](images/3530fc958f4a8a72e2d6b263066997fa.png)

若弹出如下提示，可以检查：

1.  审批流配置是否设为外部流程；
2.  表单模板定制中，是否任务字段，是否为1。

![](images/7b77d4d65a231586cae2d9efc0da64f5.png)

如果确实不需要它是任务，就修改1.2节中流程启动方式为直接。

##### 1.2.1.2 开户办理

路径：资金管理-账户管理-银行账户-开户办理

审批通过后，可以去银行完成开户操作，开户完成后，打开【开户办理】功能，选择对应待办理账户开户申请，点击【生成账户】按钮

![](images/cce9065a5d01657ba7208d4406807427.png)

![](images/fd3dd7e107276645ddbd9e2f6ca0cb4b.png)

补充填写账号、直联开通状态、子账户等账户信息，保存。

部分字段用途解释：

1.  【核算单位】：核算组织，指账户的记账组织，做业务时作为参数传给生成财务凭证方法。
2.  【授权使用单位】：行政组织，指账户授权给哪些单位使用，如分公司开的账户，授权给项目部使用；也作为做结算业务的账户的选择条件之一。需要启用账户之后才能进行授权。
3.  【对账单位】：行政组织，在银行对账时使用。
4.  【直联开通状态】：选择账户对应的银企直联开通状态：未开通、开通查询、开通查询及支付。如选择开通查询及支付，则付款时，默认勾选银企直联，可以取消。未选择开通支付则不允许选择银企直联。
5.  【零余额户】：在【资金池】功能（基础数据-财务基础数据-结算信息-资金池）中设置零余额模式时，只能选择勾选了此处的账户。
6.  【默认结算户】：如勾选，则办理时系统自动检查开户单位所开的内部账户是否已有默认结算户，如有，则弹出确认提示是否替换之前的默认结算户。如是，则后台取消之前的内部账户的默认结算户标识，当前默认账户勾选。
7.  【透支额度】：只有账户类型是活期时，才可填写，用于计算账户的可用余额。超过额度不允许支付。
8.  【单笔支付限额】【单日限额】【头寸上限】【头寸下限】填写在银行设定的对应限额，目前在系统中无实际控制。

此处若需要由单位开户申请、资金中心办理的，可以在【全局参数配置】功能中，将如下参数值改为是。

![](images/54f06a57872a955c7a9a200b47840b9e.png)

##### 1.2.1.3 开户启用

路径：资金管理-账户管理-银行账户-开户启用

选择需启用账户，点击启用按钮进行启用。

![](images/4410e2f39b4e36e9cd9b840a1cafcb4b.png)

若无需手动启用，参见[1.2基础配置-2)业务流配置](#基础配置)，将业务流配置为自动启用即可。

此处若需要由资金中心启用账户的，可以在【全局参数配置】功能中，将如下参数值改为是。

![](images/6a2be91da7628ab773c65e36b29bca8d.png)

##### 1.2.1.4 余额初始

路径：资金管理-资金基础-系统设置-账户余额初始

打开账户余额初始功能，选择需初始账户，选择初始日期（不能小于开户单位启用日期，不能大于当前日期），填写各所需条目后，点击保存以及初始完成按钮，完成初始。余额初始化只能做一次，不能进行二次初始化！

余额与积数的区别

【余额】：当前时点，账户有多少钱，为余额。

【积数】：主要用于计算利息，为当前计息期间每日余额的和。

以每月20日计息为例，

| 日期 | 余额  | 积数   |
|------|-------|--------|
| 1    | 50000 | 50000  |
| 2    | 10000 | 60000  |
| 3    | 10000 | 70000  |
| ……   | ……    | ……     |
| 20   | 10000 | 240000 |

20日计息时，以当日积数（240000）\*日利率，即为当月利息。

## 2 现金账户开户

### 2.1 基础配置

#### 2.1.1 业务流配置

路径：业务公共—共享服务—流程定义—公共流程分配

现金账户开户流程在流程分配功能中有预制流程模板，可直接导入。

![](images/f82a01a29c277f16c253514519538af5.png)

模板导入后，打开导入的流程

![](images/c2b4cdfa1d33b94c02249bad57be3c6b.png)

点击编辑按钮，根据客户实际情况，设置办理环节的参与者（可以办理此业务的岗位/用户）。设置完成后，需要保存并启用。

**注意：业务流程请务必不要自行修改，若有修改需求，务必与产品部研发沟通确认！！！**

![](images/df532ba1df4784e0c3920753f692428f.png)

#### 2.1.2 审批流配置（外部流程节点）

路径：流程平台—工作流平台—流程建模—流程设计

打开流程设计功能，在左侧资金管理-账户管理目录下找到现金账户开户，新增审批流，流程分类选择现金账户开户，启动方式选择外部流程。点击确定后，根据客户需要，设置审批流程。设置完成后保存、发布。

![](images/b4797081a4d59d31e5af653046a695e5.png)

### 2.2 业务流程

开户申请的主要流程为申请-审批-办理-启用-初始

#### 2.2.1 开户申请

**开户申请**

路径：资金管理-账户管理-银行账户-开户申请

打开开户申请功能，点击新增按钮，新增一条现金账户开户申请

![](images/e0b173be8ec9702c965f76a2ff663b71.png)

填写完成后点击保存，进行开户审批。

**开户办理**

路径：业务公共-共享服务-我的流程-待办事项

审批通过后，打开【待办事项】功能，选择对应待办理开户申请。

![](images/38ecd0aa902f065a299c4a8bca6032d8.png)

补充填写账号，点击办理即可完成办理。

![](images/29122feb4b2e9f9a324c89202c5d1767.png)

#### 2.2.2 余额初始

路径：资金管理-资金基础-系统设置-账户余额初始

打开账户余额初始功能，选择需初始账户，选择初始日期（不能小于开户单位启用日期，不能大于当前日期），填写各所需条目后，点击保存以及初始完成按钮，完成初始。余额初始化只能做一次，不能进行二次初始化！（如果未发生业务，可以取消初始，修改初始日期，重新初始）

余额与积数的区别

【余额】：当前时点，账户有多少钱，为余额。

【积数】：主要用于计算利息，为当前计息期间每日余额的和。

以每月20日计息为例，

| 日期 | 余额  | 积数   |
|------|-------|--------|
| 1    | 50000 | 50000  |
| 2    | 10000 | 60000  |
| 3    | 10000 | 70000  |
| ……   | ……    | ……     |
| 20   | 10000 | 240000 |

20日计息时，以当日积数（240000）\*日利率，即为当月利息。