# 银行账户延期

## 1 基础配置

### 1.1 业务流配置

路径：业务公共—共享服务—流程定义—公共流程分配

账户延期申请在流程分配功能中有预制流程模板，可直接导入。

![](images/bcb5dfaab27efccd3914e4755476e0fc.png)模板导入后，打开导入的流程

![](images/66dc027f9cc769de5a43e1e2eaadf316.png)

点击编辑按钮，根据客户实际情况，设置延期处理环节的参与者（可以办理此业务的岗位/用户）。设置完成后，需要保存并启用。

**注意：业务流程请务必不要自行修改，若有修改需求，务必与产品部研发沟通确认！！！**

![](images/2980ff2516408b6e344007df4cfbe245.png)

### 1.2 审批流配置（外部流程节点）

路径：流程平台—工作流平台—流程建模—流程设计

打开流程设计功能，在左侧资金管理-账户管理目录下找到银行账户延期，新增审批流，流程分类选择银行账户延期，启动方式选择外部流程。点击确定后，根据客户需要，设置审批流程。设置完成后保存、发布。

![](images/b4cd459f6cd6b4665647b70fc4078e56.png)

## 2 业务流程

### 2.1 延期申请

路径：资金管理-账户管理-银行账户-延期申请

打开账户延期申请功能，点击新增，增加一条新的延期申请。

选择需延期的单位与账户（需为临时户），并填写预计延期期限、延期说明，保存提交审批。

![](images/6312a8c91e4f4b344751f7a36751734f.png)

### 2.2 延期办理

路径：业务公共-共享服务-我的流程-待办事项

审批通过后，打开【待办事项】功能，如需调整延期期限，可以选择对应待办理申请进行修改，若无需修改，可以直接选择通过，完成账户延期。

![](images/e69c887a30f7c7bec329ee586bfdecfb.png)
