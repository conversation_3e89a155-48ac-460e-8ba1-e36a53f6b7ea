账户常见问题解答

# 账户

## 证书办理申请中，事前不填写证书编号

1）扩展表单，将事前审批放出，勾选上，提交流程，如下：

![](images/60d66ce418af53e2e3d30a9beeacd123.png)

2）公共流程分配中引入证书办理申请事前审批的流程

![](images/7450828c27cafc29b056fa1538015961.png)

3）证书办理申请勾选事前审批，保存单据可以不填写证书信息

4）待办事项中办理界面进行证书信息填写和办理

## 销户参数控制

1）销户申请检查账面余额控制方式

2）销户申请检查在途单据控制方式

3）账面有余额控制方式

4）销户检查银行余额控制方式

项目根据实际需要进行相关参数设置。

## 合作银行准入申请，新增业务范围弹窗数据为空

业务范围，通过【标准代码】功能中找到账户用途进行维护。

## 流转规则设置

1）找到银行账户开户申请单的运行时表单（记住那个扩展表单ID），打开表单设计

![](images/4335c4895d3d4e2aa482498c0dcc8128.png)

2）打开流转配置

![](images/ea22af74815180cc270fb029b835e3e0.png)

3）可以看到一条基础表单的流转规则![](images/ddd9532f66a06a8c811a3d6a3513b23a.png)
4）点击复制，新增一条扩展表单的流转规则

![](images/8884db1c577ead106c15719006c40a61.png)

5）配置新增的这条流转规则

![](images/28bf4bbe6c6d026b0d12020ae1a6daaa.png)

6）配置子户的转换关系，增加【是否允许透支】字段的映射，然后保存
![](images/8fba9ff49d9ad029de9a8c6cade5e0fa.png)
7）保存运行时表单

## 全局参数设置了校验合作银行，但是开户申请没起作用

1）全局参数如下：

![](images/f1d086c82ff6a504435f056c0ac7a6e4.png)

2）开户申请的流程选择如下：

![](images/f945d1ae623da5e264a3ee99a8329fd2.png)
